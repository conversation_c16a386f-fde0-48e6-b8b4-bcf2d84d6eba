package app

import (
	"errors"
	"net/http"
	_ "net/http/pprof"

	"secwalk/internal/config"
	xg "secwalk/internal/domain/core/xguard"
	"secwalk/internal/domain/repo"
	"secwalk/internal/domain/service/agent"
	"secwalk/internal/domain/service/mcp"
	"secwalk/internal/domain/service/session"
	"secwalk/internal/domain/service/toolkit"
	"secwalk/internal/domain/service/xguard"
	"secwalk/internal/server"
	"secwalk/pkg/db/minio"
	"secwalk/pkg/license"
	"secwalk/pkg/logger"
	"secwalk/pkg/selector"
	"secwalk/pkg/telemetry"

	"github.com/sirupsen/logrus"
	"github.com/urfave/cli/v2"
)

var ErrConfigParm = errors.New("please input config file (--config) or register address (--consul)")

func New() *cli.App {
	app := cli.NewApp()
	app.Name = "secwalk"
	app.Version = config.GetVersion()
	app.Commands = []*cli.Command{
		{
			Name:  "run",
			Usage: "run server",
			Flags: []cli.Flag{
				&cli.StringFlag{Name: "config"},
				&cli.StringFlag{Name: "consul"},
				&cli.StringFlag{Name: "token"},
				&cli.StringFlag{Name: "pprof"},
			},
			Action: runAction,
		},
	}

	return app
}

func runAction(c *cli.Context) error {
	cfgfile := c.String("config")
	cfgaddr := c.String("consul")
	token := c.String("token")
	pprof := c.String("pprof")

	if len(pprof) > 0 {
		go func() {
			logrus.Infof("run debug: http://%s/debug/pprof", pprof)
			http.ListenAndServe(pprof, nil)
		}()
	}

	if len(cfgfile) == 0 && len(cfgaddr) == 0 {
		return ErrConfigParm
	}

	// 获取配置
	if err := config.New(cfgfile, cfgaddr, token); err != nil {
		return err
	}

	// 初始化 OpenTelemetry
	telemetryConfig := config.GetTelemetry()
	otelCleanup, err := telemetry.Init(c.Context, telemetry.Config{
		ServiceName:    telemetryConfig.ServiceName,
		ServiceVersion: telemetryConfig.ServiceVersion,
		Environment:    telemetryConfig.Environment,
		OTLPEndpoint:   telemetryConfig.OTLPEndpoint,
		Enabled:        telemetryConfig.Enabled,
		SampleRate:     telemetryConfig.SampleRate,
	})
	if err != nil {
		logrus.WithField(logger.KeyCategory, logger.CategorySystem).
			Warnf("failed to initialize telemetry: %v", err)
	} else {
		defer otelCleanup()
		logrus.WithField(logger.KeyCategory, logger.CategorySystem).
			Infof("telemetry initialized successfully")
	}

	// 初始化DB客户端
	db, err := repo.NewDBClient(config.GetDB())
	if err != nil {
		return err
	}

	// 初始化minio客户端
	logrus.WithField(logger.KeyCategory, logger.CategorySystem).
		Infof("init minio client: %s", config.GetMinio().Host)
	if err := minio.Init(config.GetMinio()); err != nil {
		return err
	}

	// 服务发现
	logrus.WithField(logger.KeyCategory, logger.CategorySystem).
		Infof("init server selector")
	if err := selector.Init(config.GetServices()); err != nil {
		return err
	}

	// 初始化全局服务实例
	logrus.WithField(logger.KeyCategory, logger.CategorySystem).
		Infof("init license")
	license.Init()

	// 初始化存储repo
	logrus.WithField(logger.KeyCategory, logger.CategorySystem).
		Infof("init storage repo")
	agentRepo := repo.NewAgentRepo(db)
	termRepo := repo.NewTermRepo(db)
	xguardRepo := repo.NewXGuardRepo(db)
	toolkitRepo := repo.NewToolkitRepo(db)
	sessionRepo := repo.NewSessionRepo(db)
	signRepo := repo.NewSignRepo(db)
	mcpRepo := repo.NewMCPRepo(db)

	// 初始化特征检测
	xg, err := xg.New(c.Context, agentRepo, xguardRepo)
	if err != nil {
		return err
	}

	// 创建服务实例
	srv, err := server.New(
		agent.NewService(
			xg,
			agentRepo,
			toolkitRepo,
			sessionRepo,
			mcpRepo,
			termRepo,
		),
		toolkit.NewService(
			toolkitRepo,
		),
		session.NewService(
			sessionRepo,
		),
		xguard.NewService(
			xg,
			xguardRepo,
		),
		mcp.NewService(
			mcpRepo,
		),
		signRepo,
	)
	if err != nil {
		return err
	}

	// 服务启动
	return srv.Run()
}
