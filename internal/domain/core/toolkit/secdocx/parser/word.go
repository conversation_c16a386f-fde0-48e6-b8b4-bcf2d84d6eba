package parser

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"os"
	"path/filepath"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"secwalk/internal/domain/core/xfile"
	"strings"

	"github.com/unidoc/unioffice/v2/common"
	"github.com/unidoc/unioffice/v2/document"
)

// parseWord 解析Word文档并返回一个文档对象
func parseWord(ctx context.Context, file *format.File) (*format.Document, error) {
	// 从上下文中获取ext信息，用于图片存储
	extType := ext.New("")
	// 检查上下文是否已经结束
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
		// 继续处理
	}

	// 获取文件数据
	fileData := file.GetReader().Bytes()
	reader := bytes.NewReader(fileData)

	// 创建Word文档
	doc, err := document.Read(reader, int64(len(fileData)))
	if err != nil {
		return nil, err
	}
	defer doc.Close()

	// 创建文档
	docf := format.NewDocument(filepath.Base(file.Target), format.TypeDOC)

	// 解析文档属性
	docProperties := parseWordProperties(ctx, doc)
	for k, v := range docProperties {
		docf.Properties[k] = v
	}

	// 图片将在按顺序处理节点时处理

	// 获取文档总页数
	totalPages := int32(1) // 默认至少有一页
	if pages, ok := docProperties["pageCount"].(int32); ok && pages > 0 {
		totalPages = pages
	}

	// 使用Nodes()方法按顺序获取文档的所有元素
	docNodes := doc.Nodes()
	allNodes := docNodes.X() // 获取所有节点
	nodeCount := len(allNodes)

	// 如果文档内容较多，但只有一页，则估算页数
	if totalPages == 1 && nodeCount > 50 {
		// 根据节点数量估算页数，大约50个节点一页
		estimatedPages := nodeCount / 50
		if estimatedPages > 1 {
			totalPages = int32(estimatedPages)
		}
	}

	// 创建页码跟踪器
	pageTracker := newPageTracker(totalPages, nodeCount)

	// 首先扫描所有节点，找出所有分页符（仅针对段落）
	for i, node := range allNodes {
		if para, ok := node.X().(*document.Paragraph); ok {
			if hasPageBreakFromNode(para) {
				pageTracker.pageBreaks = append(pageTracker.pageBreaks, i)
			}
		}
	}

	// 如果没有找到分页符，但文档有多页，则强制分页
	if len(pageTracker.pageBreaks) == 0 && totalPages > 1 {
		// 强制在文档中均匀分布分页符
		nodesPerPage := nodeCount / int(totalPages)
		for i := 1; i < int(totalPages); i++ {
			pageTracker.pageBreaks = append(pageTracker.pageBreaks, i*nodesPerPage)
		}
	}

	// 按顺序处理所有节点
	for i, node := range allNodes {
		// 定期检查上下文（每10个节点）
		if i%10 == 0 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			default:
				// 继续处理
			}
		}

		// 计算当前节点的页码
		currentPage := pageTracker.getNodePageByIndex(i)

		// 根据节点类型处理
		switch element := node.X().(type) {
		case *document.Paragraph:
			// 处理段落
			if err := processParagraphNode(element, currentPage, docf, ctx, extType, doc); err != nil {
				return nil, err
			}
		case *document.Table:
			// 处理表格
			if err := processTableNode(element, currentPage, docf); err != nil {
				return nil, err
			}
			// 可以在这里添加其他类型的处理，如图片等
		}
	}

	return docf, nil
}

// processParagraphNode 处理段落节点
func processParagraphNode(para *document.Paragraph, currentPage int, docf *format.Document, ctx context.Context, extType ext.ExtType, doc *document.Document) error {
	// 获取段落文本和处理图片
	text := ""
	hasImages := false

	for _, run := range para.Runs() {
		text += run.Text()

		// 检查这个run中是否包含图片
		if err := processRunImages(ctx, run, currentPage, docf, extType); err != nil {
			return err
		}

		// 检查是否有图片
		if len(run.DrawingAnchored()) > 0 || len(run.DrawingInline()) > 0 {
			hasImages = true
		}
	}
	text = strings.TrimSpace(text)

	// 如果段落只有图片没有文本，也不跳过（图片已经处理了）
	if text == "" && !hasImages {
		return nil
	}

	// 检查段落是否为标题
	headingLevel := 0
	headingNumber := ""
	finalText := text // 直接使用原始文本，不做任何编号拼接

	// 首先尝试从文本内容中检测标题（最可靠的方法）
	contentHeadingLevel, contentHeadingNumber := detectHeading(text)
	if contentHeadingLevel > 0 {
		headingLevel = contentHeadingLevel
		headingNumber = contentHeadingNumber
	} else {
		// 如果文本检测失败，再检查段落样式
		styleHeadingLevel := getHeadingLevelFromParagraph(para)
		if styleHeadingLevel > 0 {
			headingLevel = styleHeadingLevel
			// 从编号属性获取编号信息（仅用于headingNumber，不拼接到文本）
			numberingInfo := extractNumberingFromParagraph(para, doc)
			if numberingInfo.number != "" && numberingInfo.number != "1." && numberingInfo.number != "1.1." {
				headingNumber = numberingInfo.number
			}
		} else {
			// 最后尝试编号属性（但级别必须大于0）
			numberingInfo := extractNumberingFromParagraph(para, doc)
			if numberingInfo.level > 0 {
				headingLevel = numberingInfo.level
				if numberingInfo.number != "" && numberingInfo.number != "1." && numberingInfo.number != "1.1." {
					headingNumber = numberingInfo.number
				}
			}
		}
	}

	// 创建文本块
	textBlock := format.NewTextBlock(finalText, headingLevel, headingNumber)

	// 将块添加到文档中
	docf.AddBlockToPage(currentPage, textBlock)
	return nil
}

// processRunImages 处理Run中的图片
func processRunImages(ctx context.Context, run document.Run, currentPage int, docf *format.Document, extType ext.ExtType) error {
	// 处理锚定图片（Anchored Images）
	for _, anchored := range run.DrawingAnchored() {
		if err := processDrawingImage(ctx, anchored, currentPage, docf, extType); err != nil {
			return err
		}
	}

	// 处理内联图片（Inline Images）
	for _, inline := range run.DrawingInline() {
		if err := processDrawingImage(ctx, inline, currentPage, docf, extType); err != nil {
			return err
		}
	}

	return nil
}

// processDrawingImage 处理绘图中的图片（通用处理函数）
func processDrawingImage(ctx context.Context, drawing interface{}, currentPage int, docf *format.Document, extType ext.ExtType) error {
	// 检查上下文
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// 尝试获取图片
	var imgRef interface{}
	var found bool

	// 根据类型获取图片引用
	switch d := drawing.(type) {
	case document.AnchoredDrawing:
		imgRef, found = d.GetImage()
	case document.InlineDrawing:
		imgRef, found = d.GetImage()
	default:
		return nil // 不支持的类型
	}

	if !found {
		return nil // 没有图片
	}

	// 将图片引用转换为ImageRef类型
	imageRef, ok := imgRef.(interface {
		RelID() string
		Target() string
		Path() string
		Data() *[]byte
	})
	if !ok {
		// 类型转换失败，但仍然创建一个占位符图片
		randomName := generateRandomImageName()
		return createPlaceholderImage(ctx, currentPage, docf, randomName)
	}

	// 获取图片数据
	imgData, _, err := extractImageData(imageRef)
	if err != nil {
		// 提取失败，使用占位符图片
		imgData = createMinimalPNGData()
	}

	// 如果没有图片数据，使用占位符图片
	if len(imgData) == 0 {
		imgData = createMinimalPNGData()
	}

	// 存储图片到MinIO
	xf := storeImageToMinio(ctx, imgData, "word")

	// 添加到文档（如果xf为NULL则不添加）
	addImageToDocument(docf, xf, currentPage)

	return nil
}

// extractImageData 提取图片数据
func extractImageData(imageRef interface {
	RelID() string
	Target() string
	Path() string
	Data() *[]byte
}) ([]byte, string, error) {
	// 获取文件名
	fileName := filepath.Base(imageRef.Target())
	if fileName == "" {
		fileName = fmt.Sprintf("image_%s.png", imageRef.RelID())
	}

	// 尝试从不同源获取图片数据
	var imgData []byte

	// 1. 尝试从 Data() 获取
	if imageRef.Data() != nil && len(*imageRef.Data()) > 0 {
		imgData = *imageRef.Data()
	} else if imageRef.Path() != "" {
		// 2. 尝试从文件路径读取
		data, err := os.ReadFile(imageRef.Path())
		if err == nil {
			imgData = data
		}
	}

	return imgData, fileName, nil
}

// generateRandomImageName 生成随机图片名称
func generateRandomImageName() string {
	// 生成8字节的随机数据
	bytes := make([]byte, 8)
	_, err := rand.Read(bytes)
	if err != nil {
		// 如果随机数生成失败，使用备用方案
		return fmt.Sprintf("img_%d", len(bytes)*1000+int(bytes[0]))
	}

	// 将字节转换为十六进制字符串
	return hex.EncodeToString(bytes)
}

// createPlaceholderImage 创建占位符图片
func createPlaceholderImage(ctx context.Context, currentPage int, docf *format.Document, baseName string) error {
	// 使用最小图片数据
	imgData := createMinimalPNGData()

	// 存储占位符图片到MinIO
	xf := storeImageToMinio(ctx, imgData, "word_placeholder")

	// 添加到文档（如果xf为NULL则不添加）
	addImageToDocument(docf, xf, currentPage)

	return nil
}

// createPlaceholderImageWithStorage 使用新存储方法创建占位符图片
func createPlaceholderImageWithStorage(ctx context.Context, currentPage int, docf *format.Document, baseName string) error {
	return createPlaceholderImage(ctx, currentPage, docf, baseName)
}

// processTableNode 处理表格节点
func processTableNode(tbl *document.Table, currentPage int, docf *format.Document) error {
	// 创建表格数据
	rows := len(tbl.Rows())
	cols := 0
	// 确定最大列数
	for _, row := range tbl.Rows() {
		if len(row.Cells()) > cols {
			cols = len(row.Cells())
		}
	}

	// 创建数据数组
	data := make([][]string, rows)
	for i := range data {
		data[i] = make([]string, cols)
	}

	// 处理行和单元格
	for rowIdx, row := range tbl.Rows() {
		for cellIdx, cell := range row.Cells() {
			// 获取单元格文本
			var textParts []string
			for _, para := range cell.Paragraphs() {
				paraText := ""
				for _, run := range para.Runs() {
					paraText += run.Text()
				}
				paraText = strings.TrimSpace(paraText)
				if paraText != "" {
					textParts = append(textParts, paraText)
				}
			}
			// 合并段落文本，用空格分隔
			text := strings.Join(textParts, " ")
			text = strings.TrimSpace(text)

			// 将文本添加到数据数组中
			data[rowIdx][cellIdx] = text
		}
	}

	// 检查并移除重复行
	data = RemoveDuplicateRows(data)
	rows = len(data) // 更新行数

	// 创建表格块
	tableBlock := format.NewTableBlock(rows, cols, data)

	// 将表格添加到文档中
	docf.AddBlockToPage(currentPage, tableBlock)
	return nil
}

// pageTracker 用于跟踪文档中节点的页码
type pageTracker struct {
	totalPages  int32 // 文档总页数
	totalNodes  int   // 文档总节点数
	pageBreaks  []int // 页面分隔符位置
	currentPage int   // 当前页码
}

// newPageTracker 创建一个新的页码跟踪器
func newPageTracker(totalPages int32, totalNodes int) *pageTracker {
	return &pageTracker{
		totalPages:  totalPages,
		totalNodes:  totalNodes,
		pageBreaks:  []int{},
		currentPage: 1, // 页码从1开始
	}
}

// getNodePage 计算节点所在的页码
func (pt *pageTracker) getNodePage(nodeIndex int, node *document.Paragraph) int {
	// 如果文档只有一页，所有节点都在第一页
	if pt.totalPages <= 1 {
		return 1
	}

	// 检查是否是分页符
	if node != nil && hasPageBreak(node) {
		pt.currentPage++
	}

	// 如果有分页符，使用当前页码
	if len(pt.pageBreaks) > 0 {
		// 找到当前节点应该属于的页码
		for i, breakIndex := range pt.pageBreaks {
			if nodeIndex < breakIndex {
				return i + 1 // 页码从1开始
			}
		}
		return len(pt.pageBreaks) + 1 // 如果节点在最后一个分页符之后
	} else {
		// 如果没有明确的分页符，根据节点在文档中的位置估算页码
		// 强制将文档分为多页，即使没有分页符
		estimatedPage := int(float64(nodeIndex) / float64(pt.totalNodes) * float64(pt.totalPages))
		// 确保页码在有效范围内
		if estimatedPage < 1 {
			estimatedPage = 1
		}
		if estimatedPage > int(pt.totalPages) {
			estimatedPage = int(pt.totalPages)
		}
		return estimatedPage
	}
}

// getNodePageByIndex 根据节点索引计算节点所在的页码
func (pt *pageTracker) getNodePageByIndex(nodeIndex int) int {
	// 如果文档只有一页，所有节点都在第一页
	if pt.totalPages <= 1 {
		return 1
	}

	// 如果有分页符，使用当前页码
	if len(pt.pageBreaks) > 0 {
		// 找到当前节点应该属于的页码
		for i, breakIndex := range pt.pageBreaks {
			if nodeIndex < breakIndex {
				return i + 1 // 页码从1开始
			}
		}
		return len(pt.pageBreaks) + 1 // 如果节点在最后一个分页符之后
	} else {
		// 如果没有明确的分页符，根据节点在文档中的位置估算页码
		// 强制将文档分为多页，即使没有分页符
		estimatedPage := int(float64(nodeIndex) / float64(pt.totalNodes) * float64(pt.totalPages))
		// 确保页码在有效范围内
		if estimatedPage < 1 {
			estimatedPage = 1
		}
		if estimatedPage > int(pt.totalPages) {
			estimatedPage = int(pt.totalPages)
		}
		return estimatedPage
	}
}

// hasPageBreakFromNode 检查段落是否包含分页符
func hasPageBreakFromNode(p *document.Paragraph) bool {
	return hasPageBreak(p)
}

// getHeadingLevelFromParagraph 从段落样式中获取标题级别
func getHeadingLevelFromParagraph(p *document.Paragraph) int {
	return getHeadingLevelFromStyle(p)
}

// getHeadingLevelFromStyle 从段落样式中获取标题级别
func getHeadingLevelFromStyle(p *document.Paragraph) int {
	// 获取段落样式
	props := p.Properties()
	style := props.Style()
	if style == "" {
		return 0
	}

	// 检查是否是标题样式
	style = strings.ToLower(style)
	if strings.Contains(style, "heading1") || strings.Contains(style, "heading 1") || strings.Contains(style, "title") {
		return 1
	} else if strings.Contains(style, "heading2") || strings.Contains(style, "heading 2") || strings.Contains(style, "subtitle") {
		return 2
	} else if strings.Contains(style, "heading3") || strings.Contains(style, "heading 3") {
		return 3
	} else if strings.Contains(style, "heading4") || strings.Contains(style, "heading 4") {
		return 4
	} else if strings.Contains(style, "heading5") || strings.Contains(style, "heading 5") {
		return 5
	} else if strings.Contains(style, "heading6") || strings.Contains(style, "heading 6") {
		return 6
	}

	// 检查段落的其他属性，如字体大小、加粗等
	// 这里可以根据需要添加更多的检查逻辑

	return 0
}

// hasPageBreak 检查段落是否包含分页符
func hasPageBreak(p *document.Paragraph) bool {
	// 检查段落中的所有运行
	for _, run := range p.Runs() {
		// 检查运行中的所有内容
		for _, ic := range run.X().EG_RunInnerContent {
			// 检查是否有分页符
			if ic.RunInnerContentChoice != nil && ic.RunInnerContentChoice.Br != nil {
				// 在 Word 文档中，分页符的 TypeAttr 可能是 "page" 或者其他值
				// 检查所有可能的分页符类型
				brType := ic.RunInnerContentChoice.Br.TypeAttr.String()
				if brType == "page" || brType == "column" || brType == "textWrapping" {
					return true
				}
			}
		}
	}

	// 检查段落属性中是否设置了分页符
	props := p.Properties()
	if props.X() != nil {
		// 检查 PageBreakBefore 属性
		if props.X().PageBreakBefore != nil {
			return true
		}

		// 检查段落格式中的分页设置
		if props.X().SectPr != nil {
			return true // 段落包含节分隔符，通常意味着新页的开始
		}
	}

	return false
}

// parseWordProperties 从 Word 文档中提取属性
func parseWordProperties(ctx context.Context, doc *document.Document) map[string]any {
	properties := make(map[string]any)

	// 添加基本属性
	properties["paragraphCount"] = len(doc.Paragraphs())
	properties["tableCount"] = len(doc.Tables())
	properties["imageCount"] = len(doc.Images)

	// 添加页数信息
	properties["pageCount"] = doc.AppProperties.Pages()

	// 添加页眉页尾信息
	headerInfo := extractHeaderInfo(ctx, doc)
	if len(headerInfo) > 0 {
		properties["headers"] = headerInfo
	}

	//footerInfo := extractFooterInfo(ctx, doc)
	//if len(footerInfo) > 0 {
	//	properties["footers"] = footerInfo
	//}

	return properties
}

// 旧的processImages函数已被移除，图片处理现在在processParagraphNode中按顺序进行

// RemoveDuplicateRows 移除表格中的重复行
func RemoveDuplicateRows(data [][]string) [][]string {
	if len(data) <= 1 {
		return data
	}

	// 使用map来记录已经出现的行
	seen := make(map[string]bool)
	var result [][]string

	for _, row := range data {
		// 将行转换为字符串作为键
		rowKey := strings.Join(row, "\t") // 使用tab分隔符连接列

		// 检查是否为空行（所有列都为空）
		isEmptyRow := true
		for _, cell := range row {
			if strings.TrimSpace(cell) != "" {
				isEmptyRow = false
				break
			}
		}

		// 跳过空行和重复行
		if !isEmptyRow && !seen[rowKey] {
			seen[rowKey] = true
			result = append(result, row)
		}
	}

	return result
}

// extractHeaderInfo 提取页眉信息
func extractHeaderInfo(ctx context.Context, doc *document.Document) []map[string]any {
	var headerItems []map[string]any

	// 获取所有页眉
	headers := doc.Headers()
	for _, header := range headers {
		paragraphs := header.Paragraphs()
		for _, para := range paragraphs {
			for _, run := range para.Runs() {
				// 处理文本
				text := strings.TrimSpace(run.Text())
				if text != "" {
					headerItems = append(headerItems, map[string]any{
						"content": text,
						"type":    "text",
					})
				}

				// 处理图片 - 锚定图片
				for _, anchored := range run.DrawingAnchored() {
					if imgRef, found := anchored.GetImage(); found {
						if xf := processHeaderFooterImage(ctx, &imgRef); xf != nil {
							headerItems = append(headerItems, map[string]any{
								"name": xf.String(),
								"type": "image",
							})
						}
					}
				}

				// 处理图片 - 内联图片
				for _, inline := range run.DrawingInline() {
					if imgRef, found := inline.GetImage(); found {
						if xf := processHeaderFooterImage(ctx, &imgRef); xf != nil {
							headerItems = append(headerItems, map[string]any{
								"name": xf.String(),
								"type": "image",
							})
						}
					}
				}
			}
		}
	}

	return headerItems
}

// extractFooterInfo 提取页脚信息
func extractFooterInfo(ctx context.Context, doc *document.Document) []map[string]any {
	var footerItems []map[string]any

	// 获取所有页脚
	footers := doc.Footers()
	for _, footer := range footers {
		paragraphs := footer.Paragraphs()
		for _, para := range paragraphs {
			for _, run := range para.Runs() {
				// 处理文本
				text := strings.TrimSpace(run.Text())
				if text != "" {
					footerItems = append(footerItems, map[string]any{
						"content": text,
						"type":    "text",
					})
				}

				// 处理图片 - 锚定图片
				for _, anchored := range run.DrawingAnchored() {
					if imgRef, found := anchored.GetImage(); found {
						if xf := processHeaderFooterImage(ctx, &imgRef); xf != nil {
							footerItems = append(footerItems, map[string]any{
								"name": xf.String(),
								"type": "image",
							})
						}
					}
				}

				// 处理图片 - 内联图片
				for _, inline := range run.DrawingInline() {
					if imgRef, found := inline.GetImage(); found {
						if xf := processHeaderFooterImage(ctx, &imgRef); xf != nil {
							footerItems = append(footerItems, map[string]any{
								"name": xf.String(),
								"type": "image",
							})
						}
					}
				}
			}
		}
	}

	return footerItems
}

// processHeaderFooterImage 处理页眉页脚中的图片
// 即使存储失败也要返回xfile对象，确保图片信息出现在页眉页脚中
func processHeaderFooterImage(ctx context.Context, imgRef *common.ImageRef) *xfile.File {
	// 获取图片数据
	imgData, _, err := extractImageData(imgRef)
	if err != nil || len(imgData) == 0 {
		// 提取失败，使用占位符图片
		imgData = createMinimalPNGData()
	}

	// 生成随机文件名，符合xfile格式
	fileName := generateRandomImageFileName("header_footer")

	// 创建xfile对象
	xf := xfile.New(fileName, imgData)
	xf.SetType("image")

	// 尝试存储到MinIO（失败也不影响返回xfile）
	_ = storeImageToMinio(ctx, imgData, "header_footer")

	// 无论存储是否成功，都返回xfile对象
	return xf
}

// numberingInfo 存储段落编号信息
type numberingInfo struct {
	level  int    // 编号级别
	number string // 编号文本
}

// extractNumberingFromParagraph 从段落的编号属性中提取编号信息
func extractNumberingFromParagraph(para *document.Paragraph, doc *document.Document) numberingInfo {
	// 获取段落属性
	props := para.Properties()
	if props.X() == nil || props.X().NumPr == nil {
		return numberingInfo{level: 0, number: ""}
	}

	numPr := props.X().NumPr

	// 获取编号ID和级别
	var numId int64 = -1
	var ilvl int64 = 0

	if numPr.NumId != nil {
		numId = numPr.NumId.ValAttr
	}

	if numPr.Ilvl != nil {
		ilvl = numPr.Ilvl.ValAttr
	}

	// 如果没有有效的编号ID，返回空
	if numId < 0 {
		return numberingInfo{level: 0, number: ""}
	}

	// 获取编号级别定义
	numberingLevel := doc.GetNumberingLevelByIds(numId, ilvl)
	if numberingLevel.X() == nil {
		return numberingInfo{level: 0, number: ""}
	}

	// 计算编号级别（从0开始转换为从1开始）
	level := int(ilvl) + 1

	// 尝试获取编号文本
	numberText := ""
	if lvl := numberingLevel.X(); lvl != nil {
		if lvl.LvlText != nil && lvl.LvlText.ValAttr != nil {
			// 获取编号格式文本，如 "%1."
			numberText = *lvl.LvlText.ValAttr

			// 计算实际的编号值
			numberText = calculateActualNumbering(doc, para, numId, ilvl, numberText)
		}
	}

	return numberingInfo{
		level:  level,
		number: numberText,
	}
}

// calculateActualNumbering 计算段落的实际编号值
func calculateActualNumbering(doc *document.Document, currentPara *document.Paragraph, numId, ilvl int64, formatText string) string {
	// 简化处理：直接使用级别+1作为编号
	// 这是一个临时解决方案，避免复杂的计数逻辑
	if strings.Contains(formatText, "%1") && !strings.Contains(formatText, "%2") {
		// 一级编号，如 "%1."
		return strings.ReplaceAll(formatText, "%1", "1")
	} else if strings.Contains(formatText, "%1") && strings.Contains(formatText, "%2") && !strings.Contains(formatText, "%3") {
		// 二级编号，如 "%1.%2."
		result := strings.ReplaceAll(formatText, "%1", "1")
		result = strings.ReplaceAll(result, "%2", "1")
		return result
	} else if strings.Contains(formatText, "%1") && strings.Contains(formatText, "%2") && strings.Contains(formatText, "%3") {
		// 三级编号，如 "%1.%2.%3."
		result := strings.ReplaceAll(formatText, "%1", "1")
		result = strings.ReplaceAll(result, "%2", "1")
		result = strings.ReplaceAll(result, "%3", "1")
		return result
	}

	// 如果格式不匹配，返回原始格式
	return formatText
}

// calculateActualNumberingComplex 计算段落的实际编号值（复杂版本，暂时不用）
func calculateActualNumberingComplex(doc *document.Document, currentPara *document.Paragraph, numId, ilvl int64, formatText string) string {
	// 获取文档中所有段落
	allParas := doc.Paragraphs()

	// 计算每个级别的计数器
	counters := make(map[int64]int)

	// 遍历到当前段落为止的所有段落
	for _, para := range allParas {
		// 如果是当前段落，停止计数
		if para.X() == currentPara.X() {
			break
		}

		// 检查段落是否有编号属性
		props := para.Properties()
		if props.X() == nil || props.X().NumPr == nil {
			continue
		}

		numPr := props.X().NumPr
		if numPr.NumId == nil || numPr.NumId.ValAttr != numId {
			continue // 不是同一个编号定义
		}

		paraIlvl := int64(0)
		if numPr.Ilvl != nil {
			paraIlvl = numPr.Ilvl.ValAttr
		}

		// 增加对应级别的计数器
		counters[paraIlvl]++

		// 如果是更高级别的编号（级别数字更小），重置低级别的计数器
		if paraIlvl < ilvl {
			for resetLevel := paraIlvl + 1; resetLevel <= ilvl; resetLevel++ {
				counters[resetLevel] = 0
			}
		}
	}

	// 当前段落也要计数
	counters[ilvl]++

	// 替换格式文本中的占位符
	result := formatText
	for level := int64(0); level <= ilvl; level++ {
		placeholder := fmt.Sprintf("%%%d", level+1)
		if strings.Contains(result, placeholder) {
			count := counters[level]
			if count == 0 {
				count = 1 // 默认从1开始
			}
			result = strings.ReplaceAll(result, placeholder, fmt.Sprintf("%d", count))
		}
	}

	return result
}
