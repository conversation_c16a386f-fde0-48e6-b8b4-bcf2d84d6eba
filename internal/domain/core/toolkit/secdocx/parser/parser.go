package parser

import (
	"context"
	"fmt"
	"os"
	"regexp"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"strings"
)

// Parser 表示文档解析器
type Parser struct {
	fileName  string      // 文件路径
	fileType  string      // 文件类型
	splitMode string      // 分割模式
	data      []byte      // 文件数据，如果提供则直接使用，否则从文件路径读取
	ext       ext.ExtType // 组织ID，用于从minIO获取文件
}

// New 创建一个新的文档解析器
func New(fileName string, opts ...Option) (*Parser, error) {
	p := &Parser{
		fileName:  fileName,
		splitMode: format.SplitModePage, // 默认为页面视图
	}

	for _, opt := range opts {
		opt(p)
	}

	return p, nil
}

// Parse 解析文档并返回结果为JSON字符串
func (p *Parser) Parse(ctx context.Context) (string, error) {

	resultCh := make(chan struct {
		result string
		err    error
	})

	go func() {
		// 获取文件数据
		var data []byte
		var err error

		// 如果提供了数据，直接使用
		if len(p.data) > 0 {
			data = p.data
		} else if p.fileName != "" {
			// 如果提供了组织ID，尝试从 minIO 上获取文件
			data, err = endpoint.GetFile(ctx, p.fileName, p.ext)
			if err != nil {
				// 如果从 minIO 获取失败，尝试从本地文件系统读取
				data, err = os.ReadFile(p.fileName)
				if err != nil {
					resultCh <- struct {
						result string
						err    error
					}{"", fmt.Errorf("file reading failed: %w", err)}
					return
				}
			}
		}

		// 创建文件对象
		file := format.NewFile(data, p.fileName)

		// 根据文件类型解析文档
		var docf *format.Document

		switch p.fileType {
		case format.TypePDF:
			docf, err = parsePDF(ctx, file)
		case format.TypeDOC, format.TypeXLS, format.TypePPT:
			docf, err = parseOffice(ctx, file, p.fileType)
		case format.TypeTXT, format.TypeJson:
			docf, err = parseTXT(ctx, file)
		default:
			resultCh <- struct {
				result string
				err    error
			}{"", fmt.Errorf("file type not supported: %s", p.fileType)}
			return
		}

		if err != nil {
			resultCh <- struct {
				result string
				err    error
			}{"", err}
			return
		}

		// 设置视图类型
		if p.splitMode == "toc" || p.splitMode == "tocView" {
			// 如果是目录视图，需要构建目录
			docf.BuildTocViewFromPageView()
			// 设置视图类型为目录视图
			docf.SetViewType(format.SpiltModeToc)
			// 清空页面数据，只保留目录数据
			docf.Pages = nil
		} else {
			// 默认使用页面视图
			docf.SetViewType(format.SplitModePage)
			// 清空目录数据，只保留页面数据
			docf.Headers = nil
		}

		// 转换为JSON
		result, err := docf.ToJSON()
		if err != nil {
			resultCh <- struct {
				result string
				err    error
			}{"", err}
			return
		}

		resultCh <- struct {
			result string
			err    error
		}{result, nil}
	}()

	// 等待结果或超时
	select {
	case res := <-resultCh:
		return res.result, res.err
	case <-ctx.Done():
		return "", fmt.Errorf("parser time out: %w", ctx.Err())
	}
}

// parseOffice 解析办公文档（Word、Excel、PowerPoint）
func parseOffice(ctx context.Context, file *format.File, fileType string) (*format.Document, error) {
	switch fileType {
	case format.TypeDOC:
		return parseWord(ctx, file)
	case format.TypeXLS:
		return parseExcel(ctx, file)
	case format.TypePPT:
		return parsePowerPoint(ctx, file)
	}

	return nil, fmt.Errorf("file type not supported: %s", fileType)
}

// extraFiles 将额外文件添加到文档中
func extraFiles(ctx context.Context, docf *format.Document, files []interface{}, data []byte) error {
	for _, v := range files {
		if extraFile, ok := v.(struct{ ZipPath string }); ok {
			docf.AddAttachment(extraFile.ZipPath, "file", extraFile.ZipPath, "application/octet-stream", "")
		}
	}
	return nil
}

// detectHeading 尝试检测文本是否为标题，并返回其级别和编号
// 这是一个共享函数，被PDF和Word解析器使用
func detectHeading(text string) (int, string) {
	// 排除列表项：以列表符号开头的不是标题
	if strings.HasPrefix(text, "•") || strings.HasPrefix(text, "●") ||
	   strings.HasPrefix(text, "-") || strings.HasPrefix(text, "*") ||
	   strings.HasPrefix(text, "◦") || strings.HasPrefix(text, "▪") {
		return 0, ""
	}

	// 检查数字标题模式，如 "1. 标题", "1.1 标题", "1.1.1 标题", "3.在布局了", "3.1.工具" 等
	// 支持有空格和无空格的情况
	numHeadingRegex := regexp.MustCompile(`^(\d+(\.\d+)*\.?)\s*(.+)$`)
	if matches := numHeadingRegex.FindStringSubmatch(text); len(matches) > 0 {
		titleContent := strings.TrimSpace(matches[3])

		// 标题内容验证：
		// 1. 标题不应该太长（超过100个字符可能不是标题）
		if len(titleContent) > 100 {
			return 0, ""
		}

		// 2. 标题不应该包含过多的标点符号（可能是正文内容）
		punctuationCount := strings.Count(titleContent, "，") + strings.Count(titleContent, "。") +
						   strings.Count(titleContent, "；") + strings.Count(titleContent, "：") +
						   strings.Count(titleContent, ",") + strings.Count(titleContent, ".") +
						   strings.Count(titleContent, ";") + strings.Count(titleContent, ":")
		if punctuationCount > 2 {
			return 0, ""
		}

		// 3. 标题不应该包含换行符
		if strings.Contains(titleContent, "\n") {
			return 0, ""
		}

		// 根据点的数量确定标题级别
		level := 1
		numberPart := matches[1]
		// 移除末尾的点来计算级别
		cleanNumber := strings.TrimSuffix(numberPart, ".")
		if strings.Contains(cleanNumber, ".") {
			level = len(strings.Split(cleanNumber, "."))
		}
		return level, numberPart
	}

	// 检查第几章/节模式，如 "第一章 标题", "第二节 标题" 等
	chapterRegex := regexp.MustCompile(`^\s*第([一二三四五六七八九十百千万]+|一十|二十|\d+)\s*([章节条])\s*[:：]?\s*(.+)$`)
	if matches := chapterRegex.FindStringSubmatch(text); len(matches) > 0 {
		// 根据章节类型确定级别
		level := 1
		if matches[2] == "节" {
			level = 2
		} else if matches[2] == "条" {
			level = 3
		}
		return level, matches[1] + matches[2]
	}

	// 检查中文数字标题模式，如 "一、标题", "（一）标题" 等
	chineseHeadingRegex := regexp.MustCompile(`^\s*[（(]?([一二三四五六七八九十]+)[)）]?[、.．]?\s*(.+)$`)
	if matches := chineseHeadingRegex.FindStringSubmatch(text); len(matches) > 0 {
		return 1, matches[1]
	}

	// 检查罗马数字标题模式，如 "I. 标题", "II. 标题" 等
	romanHeadingRegex := regexp.MustCompile(`^\s*([IVXivx]+)[.\s]\s*(.+)$`)
	if matches := romanHeadingRegex.FindStringSubmatch(text); len(matches) > 0 {
		return 1, matches[1]
	}

	// 检查字母标题模式，如 "A. 标题", "B. 标题" 等
	letterHeadingRegex := regexp.MustCompile(`^\s*([A-Za-z])[.\s]\s*(.+)$`)
	if matches := letterHeadingRegex.FindStringSubmatch(text); len(matches) > 0 {
		return 1, matches[1]
	}

	// 检查特殊标题模式，如文本较短且以冒号结尾
	if len(text) < 50 && (strings.HasSuffix(text, ":") || strings.HasSuffix(text, "：")) {
		return 1, ""
	}

	// 检查文本特征，如全部大写或特殊关键词
	if len(text) < 100 {
		// 全部大写的英文标题
		if text == strings.ToUpper(text) && regexp.MustCompile(`[A-Z]`).MatchString(text) {
			return 1, ""
		}

		// 包含特定关键词的标题
		headingKeywords := []string{"摘要", "摘要", "关键词", "引言", "结论", "背景", "方法", "结果", "讨论", "参考文献", "附录"}
		for _, keyword := range headingKeywords {
			if strings.Contains(text, keyword) && len(text) < 20 {
				return 1, ""
			}
		}
	}

	return 0, "" // 不是标题
}
