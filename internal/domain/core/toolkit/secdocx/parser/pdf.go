package parser

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"image"
	"image/png"
	"path/filepath"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"strings"

	"github.com/unidoc/unipdf/v3/extractor"
	"github.com/unidoc/unipdf/v3/model"
)

// parsePDF 解析PDF文件并返回一个文档对象
func parsePDF(ctx context.Context, file *format.File) (*format.Document, error) {
	// 从上下文中获取ext信息，用于图片存储
	extType := ext.New("")

	// 检查上下文是否已经结束
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
	}

	// 获取文件数据
	fileData := file.GetReader().Bytes()

	// 创建PDF读取器
	reader := bytes.NewReader(fileData)
	pdf, err := model.NewPdfReader(reader)
	if err != nil {
		return nil, err
	}

	// 创建文档
	docf := format.NewDocument(filepath.Base(file.Target), format.TypePDF)

	// 解析文档属性
	for k, v := range parsePDFProperties(pdf) {
		docf.Properties[k] = v
	}

	// PDF页眉页脚检测暂时禁用
	// 原因：基于重复内容的检测不够准确，会产生大量噪音
	// TODO: 考虑实现更简单可靠的检测方法或允许用户手动配置



	// 获取页数
	numPages, err := pdf.GetNumPages()
	if err != nil {
		return nil, err
	}

	// 处理每一页
	for i := 1; i <= numPages; i++ {
		// 在处理每页之前检查上下文是否已经结束
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			// 继续处理
		}

		page, err := pdf.GetPage(i)
		if err != nil {
			return nil, err
		}

		// 提取页面内容（包括文本和图片）
		err = extractPDFPageContent(ctx, docf, page, i, extType)
		if err != nil {
			return nil, err
		}
	}

	return docf, nil
}

// parsePDFProperties 从 PDF 文档中提取属性
func parsePDFProperties(pdf *model.PdfReader) map[string]any {
	properties := make(map[string]any)

	// 获取页数
	if numPages, err := pdf.GetNumPages(); err == nil {
		properties["pageCount"] = numPages
	}

	return properties
}

// extractPDFPageContent 从 PDF 页面中提取内容（文本和图片）
func extractPDFPageContent(ctx context.Context, docf *format.Document, page *model.PdfPage, pageNum int, extType ext.ExtType) error {
	// 检查上下文是否已经结束
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
		// 继续处理
	}

	// 创建提取器
	ex, err := extractor.New(page)
	if err != nil {
		return err
	}

	// 提取文本
	text, err := ex.ExtractText()
	if err != nil {
		return err
	}

	// 在可能耗时的操作后再次检查上下文
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:

	}

	// 如果文本仍然为空，跳过这一页
	if strings.TrimSpace(text) == "" {
		return nil
	}

	// 将提取的文本解析为块
	blocks := parsePDFText(text)

	// 如果没有有效的块，跳过
	if len(blocks) == 0 {
		return nil
	}

	// 将块添加到文档中
	for _, block := range blocks {
		docf.AddBlockToPage(pageNum, block)
	}

	// 提取页面中的图片
	if err := extractPDFPageImages(ctx, docf, page, pageNum, extType); err != nil {
		return err
	}

	return nil
}



// parsePDFText 将PDF文本解析为块
func parsePDFText(text string) []format.Block {
	var blocks []format.Block

	// 预处理：合并分散的列表项
	text = preprocessPDFText(text)

	// 按双换行符分割段落
	paragraphs := strings.Split(text, "\n\n")

	// 处理每个段落
	for _, paragraph := range paragraphs {
		paragraph = strings.TrimSpace(paragraph)
		if paragraph == "" {
			continue
		}

		// 检查段落是否为标题
		headingLevel, headingNumber := detectHeading(paragraph)
		finalText := paragraph // 直接使用原始文本，不做任何编号拼接

		// 创建文本块
		textBlock := format.NewTextBlock(finalText, headingLevel, headingNumber)

		blocks = append(blocks, textBlock)
	}

	return blocks
}

// preprocessPDFText 预处理PDF文本，合并分散的列表项
func preprocessPDFText(text string) string {
	lines := strings.Split(text, "\n")
	var result []string
	var i = 0

	for i < len(lines) {
		line := strings.TrimSpace(lines[i])

		// 检查是否是单独的列表符号
		if line == "•" || line == "●" || line == "-" || line == "*" {
			// 查找后续的内容行
			var listContent []string
			j := i + 1

			// 跳过空行
			for j < len(lines) && strings.TrimSpace(lines[j]) == "" {
				j++
			}

			// 收集列表内容，直到遇到下一个段落分隔符或文件结束
			for j < len(lines) {
				nextLine := strings.TrimSpace(lines[j])
				if nextLine == "" {
					// 检查是否是段落结束（连续空行或下一行是新的内容）
					if j+1 < len(lines) && strings.TrimSpace(lines[j+1]) == "" {
						break // 遇到双空行，段落结束
					}
					if j+1 >= len(lines) {
						break // 文件结束
					}
					// 单空行，继续收集
					listContent = append(listContent, "")
				} else {
					listContent = append(listContent, nextLine)
				}
				j++
			}

			// 合并列表项
			if len(listContent) > 0 {
				// 移除末尾的空行
				for len(listContent) > 0 && listContent[len(listContent)-1] == "" {
					listContent = listContent[:len(listContent)-1]
				}

				if len(listContent) > 0 {
					combinedContent := line + strings.Join(listContent, "\n")
					result = append(result, combinedContent)
				}
			} else {
				result = append(result, line)
			}

			i = j
		} else {
			result = append(result, line)
			i++
		}
	}

	return strings.Join(result, "\n")
}

// extractPDFPageImages 从 PDF 页面中提取图片
func extractPDFPageImages(ctx context.Context, docf *format.Document, page *model.PdfPage, pageNum int, extType ext.ExtType) error {
	// 检查上下文
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// 创建图片提取器
	imageExtractor, err := extractor.New(page)
	if err != nil {
		return err
	}

	// 提取页面中的所有图片
	images, err := imageExtractor.ExtractPageImages(nil)
	if err != nil {
		// 如果提取图片失败，不返回错误，只是跳过
		return nil
	}

	// 处理每个图片
	for _, img := range images.Images {
		// 检查上下文
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// 获取图片数据
		imgData, err := img.Image.ToGoImage()
		if err != nil {
			// 图片转换失败，跳过这个图片
			continue
		}

		// 将Go图片转换为字节数组
		imgBytes, err := imageToBytes(imgData)
		if err != nil || len(imgBytes) == 0 {
			// 转换失败或空数据，跳过这个图片
			continue
		}

		// 存储图片到MinIO
		xf := storeImageToMinio(ctx, imgBytes, "pdf")

		// 添加到文档（如果xf为NULL则不添加）
		addImageToDocument(docf, xf, pageNum)
	}

	return nil
}

// imageToBytes 将Go图片转换为字节数组
func imageToBytes(img image.Image) ([]byte, error) {
	var buf bytes.Buffer
	err := png.Encode(&buf, img)
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}



// generateRandomImageNameForPDF 为PDF生成随机图片名称
func generateRandomImageNameForPDF() string {
	// 生成8字节的随机数据
	bytes := make([]byte, 8)
	_, err := rand.Read(bytes)
	if err != nil {
		// 如果随机数生成失败，使用备用方案
		return fmt.Sprintf("pdf_img_%d", len(bytes)*1000+int(bytes[0]))
	}

	// 将字节转换为十六进制字符串，加上pdf前缀以区分
	return fmt.Sprintf("pdf_%s", hex.EncodeToString(bytes))
}
