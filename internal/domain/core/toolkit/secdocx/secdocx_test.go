package secdocx

import (
	"context"
	"os"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/toolkit/secdocx/format"
	"testing"
	"time"
)

// 测试文件路径
const (
	sampleXlsx = "../../../../../testdata/sample/中央研究院工位分布图1219.xlsx"
	samplePptx = "../../../../../testdata/sample/工作流智能体与自主型智能体解析.pptx"
	samplePdf  = "../../../../../testdata/sample/file.pdf"
	sampleDocx = "../../../../../testdata/sample/file.docx"
	sampleTxt  = "../../../../../testdata/sample/file.txt"
)

// 创建测试上下文和扩展信息
func setupTest() (context.Context, ext.ExtType) {

	//创建超时时间为10分钟的上下文
	ctx, _ := context.WithTimeout(context.Background(), 10*60*time.Second)
	return ctx, ext.ExtType{}
}

// 测试 Word 文档解析
func TestDocxParsing(t *testing.T) {
	ctx, extType := setupTest()

	// 创建工具实例
	tool, err := New(schema.WithToolConfig(&schema.ToolConfig{
		InputParameters: []schema.Parameter{
			{
				Name:  ParamFileName,
				Value: &schema.ExprValue{Type: "literal", Expr: sampleDocx},
			},
			{
				Name:  ParamFileType,
				Value: &schema.ExprValue{Type: "literal", Expr: "doc"},
			},
			{
				Name:  ParamSplitMode,
				Value: &schema.ExprValue{Type: "literal", Expr: format.SpiltModeToc},
			},
		},
	}))
	if err != nil {
		t.Fatalf("Failed to create tool: %v", err)
	}

	// 调用工具
	inputs := map[string]any{
		ParamFileName:  sampleDocx,
		"type":         "doc",
		ParamSplitMode: format.SpiltModeToc,
	}
	result, err := tool.Call(ctx, inputs, schema.WithEXT(extType))
	os.WriteFile("docx_result.json", []byte(result), 0644)
	if err != nil {
		t.Fatalf("Failed to call tool: %v", err)
	}

	// 验证结果
	if result == "" {
		t.Error("Expected non-empty result")
	}

	t.Logf("Successfully parsed DOCX file, result length: %d", len(result))
}

// 测试 Excel 文档解析
func TestXlsxParsing(t *testing.T) {
	ctx, extType := setupTest()

	// 创建工具实例
	tool, err := New(schema.WithToolConfig(&schema.ToolConfig{
		InputParameters: []schema.Parameter{
			{
				Name:  ParamFileName,
				Value: &schema.ExprValue{Type: "literal", Expr: sampleXlsx},
			},
			{
				Name:  ParamFileType,
				Value: &schema.ExprValue{Type: "literal", Expr: "xlsx"},
			},
		},
	}))
	if err != nil {
		t.Fatalf("Failed to create tool: %v", err)
	}

	// 调用工具
	inputs := map[string]any{
		ParamFileName: sampleXlsx,
		"type":        "xls",
	}
	result, err := tool.Call(ctx, inputs, schema.WithEXT(extType))
	if err != nil {
		t.Fatalf("Failed to call tool: %v", err)
	}

	// 验证结果
	if result == "" {
		t.Error("Expected non-empty result")
	}

	t.Logf("Successfully parsed XLSX file, result length: %d", len(result))
}

// 测试 PowerPoint 文档解析
func TestPptxParsing(t *testing.T) {
	ctx, extType := setupTest()

	// 创建工具实例
	tool, err := New(schema.WithToolConfig(&schema.ToolConfig{
		InputParameters: []schema.Parameter{
			{
				Name:  ParamFileName,
				Value: &schema.ExprValue{Type: "literal", Expr: samplePptx},
			},
			{
				Name:  ParamFileType,
				Value: &schema.ExprValue{Type: "literal", Expr: "ppt"},
			},
		},
	}))
	if err != nil {
		t.Fatalf("Failed to create tool: %v", err)
	}

	// 调用工具
	inputs := map[string]any{
		ParamFileName: samplePptx,
		"type":        "ppt",
	}
	result, err := tool.Call(ctx, inputs, schema.WithEXT(extType))
	os.WriteFile("pdf_result.json", []byte(result), 0644)
	if err != nil {
		t.Fatalf("Failed to call tool: %v", err)
	}

	// 验证结果
	if result == "" {
		t.Error("Expected non-empty result")
	}

	t.Logf("Successfully parsed PPTX file, result length: %d", len(result))
}

// 测试 TXT 文档解析
func TestTxtParsing(t *testing.T) {
	ctx, extType := setupTest()

	// 创建工具实例
	tool, err := New(schema.WithToolConfig(&schema.ToolConfig{
		InputParameters: []schema.Parameter{
			{
				Name:  ParamFileName,
				Value: &schema.ExprValue{Type: "literal", Expr: sampleTxt},
			},
			{
				Name:  ParamFileType,
				Value: &schema.ExprValue{Type: "literal", Expr: format.TypeTXT},
			},
		},
	}))
	if err != nil {
		t.Fatalf("Failed to create tool: %v", err)
	}

	// 调用工具
	inputs := map[string]any{
		ParamFileName: sampleTxt,
		ParamFileType: format.TypeTXT,
	}
	result, err := tool.Call(ctx, inputs, schema.WithEXT(extType))
	if err != nil {
		t.Fatalf("Failed to call tool: %v", err)
	}

	// 验证结果
	if result == "" {
		t.Error("Expected non-empty result")
	}

	t.Logf("Successfully parsed txt file, result length: %d", len(result))
}

// 测试 PDF 文档解析
func TestPdfParsing(t *testing.T) {
	ctx, extType := setupTest()

	// 创建工具实例
	tool, err := New(schema.WithToolConfig(&schema.ToolConfig{
		InputParameters: []schema.Parameter{
			{
				Name:  ParamFileName,
				Value: &schema.ExprValue{Type: "literal", Expr: samplePdf},
			},
			{
				Name:  ParamFileType,
				Value: &schema.ExprValue{Type: "literal", Expr: "pdf"},
			},
			{
				Name:  ParamSplitMode,
				Value: &schema.ExprValue{Type: "literal", Expr: format.SplitModePage},
			},
		},
	}))
	if err != nil {
		t.Fatalf("Failed to create tool: %v", err)
	}

	// 调用工具
	inputs := map[string]any{
		ParamFileName:  samplePdf,
		"type":         "pdf",
		ParamSplitMode: format.SpiltModeToc,
	}
	result, err := tool.Call(ctx, inputs, schema.WithEXT(extType))
	os.WriteFile("pdf_result.json", []byte(result), 0644)
	if err != nil {
		t.Fatalf("Failed to call tool: %v", err)
	}

	// 验证结果
	if result == "" {
		t.Error("Expected non-empty result")
	}

	t.Logf("Successfully parsed PDF file, result length: %d", len(result))
}
