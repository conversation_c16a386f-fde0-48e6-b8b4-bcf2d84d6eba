package restful

import (
	"bufio"
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"secwalk/internal/domain/core/callback"
	"secwalk/internal/domain/core/endpoint"
	"secwalk/internal/domain/core/schema"
	"secwalk/internal/domain/core/schema/ext"
	"secwalk/internal/domain/core/xfile"
	"secwalk/pkg/logger"
	"secwalk/pkg/random"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

type Restful struct {
	cfg schema.ToolConfig
	tr  *http.Transport
	cli *http.Client
}

func New(opts ...schema.ToolOption) (schema.Tool, error) {
	var cfg schema.ToolConfig
	for _, opt := range opts {
		opt(&cfg)
	}

	tr := &http.Transport{
		TLSClientConfig:     &tls.Config{InsecureSkipVerify: true},
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 50,
		IdleConnTimeout:     90 * time.Second,
	}
	cli := &http.Client{Transport: tr}

	return &Restful{
		cfg: cfg,
		tr:  tr,
		cli: cli,
	}, nil
}

// 工具配置
func (c *Restful) Config() schema.ToolConfig {
	return c.cfg
}

// 工具调用
func (c *Restful) Call(ctx context.Context, inputs map[string]any,
	opts ...schema.CallOption) (string, error) {
	opt := schema.NewCallOptions()
	for _, o := range opts {
		o(opt)
	}

	url, err := url.Parse(c.cfg.Url + c.cfg.Path)
	if strings.Contains(c.cfg.Url, "hn-rag-engine") {
		url, err = url.Parse("http://************:18092" + c.cfg.Path)
	}
	if err != nil {
		return "", err
	}

	body := make(map[string]*schema.Value)
	headers := make(map[string]string)

	form := false
	files := make(map[string]*xfile.File)

	logrus.WithField(logger.KeyCategory, logger.CategoryCore).
		WithField(logger.KeyEXT, opt.EXT).
		Infof("[%s] restful check ability params", opt.NodeID)
	for _, v := range c.cfg.InputParameters {
		value, err := v.GetValue(inputs)
		if err != nil {
			if !v.Required {
				continue
			}
			return "", fmt.Errorf("missing required parameter: %s", v.Name)
		}

		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Debugf("[%s] ability params: %s, %s, %v", opt.NodeID, v.Name, v.Type, value)

		if v.Type == schema.TypeFile || v.Type == schema.TypeFiles {
			fs := value.Files()

			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, opt.EXT).
				Debugf("[%s] ability params files: %v", opt.NodeID, fs)

			for _, f := range fs {
				file, err := xfile.Parse(f)
				if err != nil {
					return "", err
				}

				data, err := endpoint.GetFile(ctx, f, opt.EXT)
				if err != nil {
					return "", err
				}
				file.Content = data
				files[v.Name] = file
			}

			form = true
			continue
		}

		if v.Location == schema.LocationBody {
			body[v.Name] = value
			continue
		}

		if v.Location == schema.LocationPath && len(value.String()) > 0 {
			url = url.JoinPath(value.String())
			continue
		}

		if v.Location == schema.LocationQuery && len(value.String()) > 0 {
			query := url.Query()
			query.Add(v.Name, value.String())
			url.RawQuery = query.Encode()
			continue
		}

		if v.Location == schema.LocationHeader && len(value.String()) > 0 {
			headers[v.Name] = value.String()
			continue
		}
	}

	var req *http.Request

	if form {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Infof("[%s] restful form request: %s", opt.NodeID, url.String())

		bodyBuffer := &bytes.Buffer{}
		bodyWriter := multipart.NewWriter(bodyBuffer)

		for k, v := range files {
			part, err := bodyWriter.CreateFormFile(k, v.Name)
			if err != nil {
				return "", err
			}

			_, err = part.Write(v.Content)
			if err != nil {
				return "", err
			}
		}

		for k, v := range body {
			bodyWriter.WriteField(k, v.String())
		}

		bodyWriter.Close()

		req, err = http.NewRequestWithContext(
			ctx,
			c.cfg.Method,
			url.String(),
			bodyBuffer,
		)
		if err != nil {
			return "", err
		}

		req.Header.Set("Content-Type", bodyWriter.FormDataContentType())
	} else {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Infof("[%s] restful json request: %s", opt.NodeID, url.String())

		var bodyBuffer io.Reader
		var bodyWriter = map[string]any{}

		for k, v := range body {
			bodyWriter[k] = v.V()
		}

		if len(bodyWriter) != 0 {
			data, err := json.Marshal(bodyWriter)
			if err != nil {
				return "", err
			}

			logrus.WithField(logger.KeyCategory, logger.CategoryCore).
				WithField(logger.KeyEXT, opt.EXT).
				Debugf("[%s] restful json request body: %s", opt.NodeID, string(data))

			bodyBuffer = bytes.NewReader(data)
		}

		req, err = http.NewRequestWithContext(
			ctx,
			c.cfg.Method,
			url.String(),
			bodyBuffer,
		)
		if err != nil {
			return "", err
		}
	}

	for k, v := range headers {
		if len(files) > 0 && strings.ToLower(k) == "content-type" {
			continue
		}
		req.Header.Add(k, v)
	}

	if val := opt.EXT.GetValue(ext.EXTTID); len(val) > 0 {
		req.Header.Add("X-TID", val)
	}

	if val := opt.EXT.GetValue(ext.EXTXUID); len(val) > 0 {
		req.Header.Add("X-UID", val)
	}

	if val := opt.EXT.GetValue(ext.EXTXORG); len(val) > 0 {
		req.Header.Add("X-ORG", val)
	}

	if val := opt.EXT.GetValue(ext.EXTPrinciple); len(val) > 0 {
		req.Header.Add("platformPrinciple", val)
	}

	if val := opt.EXT.GetValue(ext.EXTORDER); len(val) > 0 {
		req.Header.Add("ORDER", val)
	}

	if val := opt.EXT.ToString(); len(val) > 0 {
		req.Header.Add("EXT", val)
	}

	// LLM增加请求头，用于计费
	if c.cfg.LLMFlag {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Infof("[%s] add llm flag header", opt.NodeID)
	}

	resp, err := c.cli.Do(req)
	if err != nil {
		return "", err
	}

	if len(c.Config().OutputParameters) == 1 && c.Config().OutputParameters[0].Type == schema.TypeFile {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", err
		}
		xf := xfile.New("", body)
		xf.SetNameFromHeader(resp.Header.Get("Content-Disposition"))
		s, err := xf.Dump()
		if err != nil {
			return "", err
		}
		resp.Body = io.NopCloser(bytes.NewReader(s))
	}

	defer resp.Body.Close()
	defer c.tr.CloseIdleConnections()

	if resp.StatusCode != http.StatusOK {
		resBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", err
		}
		return "", fmt.Errorf("http status code: %d, error: %s", resp.StatusCode, resBody)
	}

	if c.cfg.Stream {
		logrus.WithField(logger.KeyCategory, logger.CategoryCore).
			WithField(logger.KeyEXT, opt.EXT).
			Infof("[%s] restful stream reader", opt.NodeID)

		messageID := random.UniqueID()
		line := ""
		reader := bufio.NewReader(resp.Body)
		cout := 0
		brek := false
		once := sync.Once{}

		for {
			rawLine, err := reader.ReadString('\n')
			if err != nil {
				if err == io.EOF {
					brek = true
				} else {
					return "", err
				}
			}

			noSpaceline := strings.TrimSpace(rawLine)
			noDatasLine := strings.TrimPrefix(noSpaceline, "data:")
			noSpaceline = strings.TrimSpace(noDatasLine)

			// 停止词退出
			if noSpaceline == "[DONE]" {
				break
			}

			if len(noSpaceline) == 0 {
				// 连续多次空字符串退出
				if cout > 100 {
					logrus.WithField(logger.KeyCategory, logger.CategoryCore).
						WithField(logger.KeyEXT, opt.EXT).
						Warnf("[%s] too much empty stream answer", opt.NodeID)
					break
				}
				cout++
				continue
			} else {
				cout = 0
			}

			line = noSpaceline
			content := ""
			if c.cfg.StreamContentExpr != nil {
				kvs := make(map[string]any, 0)
				if err := json.Unmarshal([]byte(line), &kvs); err == nil {
					sv, err := c.cfg.StreamContentExpr.GetValue(kvs)
					if err == nil {
						content = sv.String()
					}
				}
			}

			if opt.FnStreamPreview != nil && len(content) > 0 {
				opt.FnStreamPreview(&callback.PreviewMessage{
					Type:      callback.TypeInline,
					From:      callback.FromRestfulAnswer,
					NodeID:    opt.MessageNodeID,
					Name:      opt.Name,
					Timestamp: time.Now().UnixMilli(),
					MessageID: messageID,
					Content:   content,
				})
			}

			once.Do(func() {
				logrus.WithField(logger.KeyCategory, logger.CategoryCore).
					WithField(logger.KeyEXT, opt.EXT).
					Infof("[%s] first resuful stream: %s", opt.NodeID, line)
			})

			if brek {
				break
			}
		}
		return line, nil
	} else {
		resBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", err
		}

		return string(resBody), nil
	}
}
