package handler

import (
	"net/http"
	"secwalk/internal/config"
	"time"

	"github.com/gin-gonic/gin"
)

type HealthHandler struct{}

func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

func (h *HealthHandler) RegisterHandler(g *gin.RouterGroup) {
	g.GET("/health", h.Health)
	g.GET("/ready", h.Ready)
}

// Health 健康检查端点
func (h *HealthHandler) Health(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Unix(),
		"service":   config.GetServer().Name,
		"version":   config.GetVersion(),
	})
}

// Ready 就绪检查端点
func (h *HealthHandler) Ready(c *gin.Context) {
	// 这里可以添加更复杂的就绪检查逻辑
	// 比如检查数据库连接、外部服务等
	
	c.<PERSON>(http.StatusOK, gin.H{
		"status":    "ready",
		"timestamp": time.Now().Unix(),
		"service":   config.GetServer().Name,
		"version":   config.GetVersion(),
		"telemetry": gin.H{
			"enabled": config.GetTelemetry().Enabled,
			"endpoint": config.GetTelemetry().OTLPEndpoint,
		},
	})
}
