package handler

import (
	"secwalk/internal/config"
	"secwalk/internal/domain/repo"
	"secwalk/internal/domain/service/agent"
	"secwalk/internal/domain/service/mcp"
	"secwalk/internal/domain/service/session"
	"secwalk/internal/domain/service/toolkit"
	"secwalk/internal/domain/service/xguard"
	"secwalk/internal/server/middleware/auth"
	logger1 "secwalk/internal/server/middleware/logger"
	"secwalk/pkg/logger"

	"github.com/gin-contrib/requestid"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

type Register interface {
	RegisterHandler(g *gin.RouterGroup)
}

func Init(agent *agent.Service, toolkit *toolkit.Service,
	session *session.Service, xguard *xguard.Service,
	mcp *mcp.Service, signRepo *repo.SignRepo) any {
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	router.Use(gin.Recovery())
	router.Use(requestid.New())
	router.Use(logger1.NewLogger())

	// 添加 OpenTelemetry 中间件
	telemetryConfig := config.GetTelemetry()
	if telemetryConfig.Enabled {
		serviceName := telemetryConfig.ServiceName
		if serviceName == "" {
			serviceName = "secwalk"
		}
		router.Use(otelgin.Middleware(serviceName))
	}

	group1 := router.Group("/secwalk/v1")
	// 注册健康检查接口
	NewHealthHandler().RegisterHandler(group1)
	// 注册智能体接口
	NewAgentHandler(agent).RegisterHandler(group1)
	// 注册工具管理接口
	NewToolkitHandler(toolkit).RegisterHandler(group1)
	// 注册会话管理接口
	NewSessionHandler(session).RegisterHandler(group1)
	// 注册特征库接口
	NewXGuardHandler(xguard).RegisterHandler(group1)
	// 注册MCP接口
	NewMCPHandler(mcp).RegisterHandler(group1)

	// 注册API接口
	group2 := router.Group("/api/v1")
	group2.Use(auth.Auth(signRepo))
	NewAPIHandler().RegisterHandler(group2)

	for _, ri := range router.Routes() {
		logrus.WithField(logger.KeyCategory, logger.CategorySystem).
			Infof("register route: %-5s %-30s --- %s", ri.Method, ri.Path, ri.Handler)
	}
	return router
}
