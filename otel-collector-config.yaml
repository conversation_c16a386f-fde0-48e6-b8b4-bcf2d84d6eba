receivers:
  # OTLP receiver for traces and metrics
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318
  
  # Prometheus receiver for scraping metrics
  prometheus:
    config:
      scrape_configs:
        - job_name: 'secwalk-service'
          static_configs:
            - targets: ['secwalk:8998']  # 您的服务地址
          metrics_path: '/metrics'       # 如果您的服务暴露了 Prometheus metrics
          scrape_interval: 30s

  # Health check receiver
  hostmetrics:
    collection_interval: 60s
    scrapers:
      cpu:
      disk:
      filesystem:
      memory:
      network:
      process:

processors:
  # Batch processor for better performance
  batch:
    timeout: 1s
    send_batch_size: 1024
    send_batch_max_size: 2048

  # Resource processor to add metadata
  resource:
    attributes:
      - key: service.name
        value: secwalk
        action: upsert
      - key: service.version
        value: v1.0.0
        action: upsert
      - key: deployment.environment
        value: production  # 根据您的环境调整
        action: upsert

  # Memory limiter to prevent OOM
  memory_limiter:
    limit_mib: 512

exporters:
  # AWS X-Ray for traces
  awsxray:
    region: us-east-1  # 根据您的 AWS 区域调整
    no_verify_ssl: false

  # AWS CloudWatch for metrics
  awscloudwatchmetrics:
    region: us-east-1  # 根据您的 AWS 区域调整
    namespace: SecWalk/Application
    dimension_rollup_option: NoDimensionRollup
    metric_declarations:
      - dimensions: [[service.name], [service.name, service.version]]
        metric_name_selectors:
          - ".*"

  # CloudWatch Logs for logs
  awscloudwatchlogs:
    region: us-east-1  # 根据您的 AWS 区域调整
    log_group_name: /aws/secwalk/application
    log_stream_name: secwalk-{instance_id}

  # Logging exporter for debugging
  logging:
    loglevel: info

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, resource, batch]
      exporters: [awsxray, logging]
    
    metrics:
      receivers: [otlp, prometheus, hostmetrics]
      processors: [memory_limiter, resource, batch]
      exporters: [awscloudwatchmetrics, logging]
    
    logs:
      receivers: [otlp]
      processors: [memory_limiter, resource, batch]
      exporters: [awscloudwatchlogs, logging]

  extensions: []
  telemetry:
    logs:
      level: info
    metrics:
      address: 0.0.0.0:8888
