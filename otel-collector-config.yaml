receivers:
  # OTLP receiver for traces and metrics from your services
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

  # Prometheus receiver for scraping metrics (if your service exposes /metrics)
  prometheus:
    config:
      scrape_configs:
        - job_name: 'secwalk-service'
          static_configs:
            - targets: ['hn-secwalk:8998']  # 您的服务容器名和端口
          metrics_path: '/metrics'
          scrape_interval: 30s

  # Host metrics for infrastructure monitoring
  hostmetrics:
    collection_interval: 60s
    scrapers:
      cpu:
      disk:
      filesystem:
      memory:
      network:
      process:

processors:
  # Batch processor for better performance
  batch:
    timeout: 1s
    send_batch_size: 1024
    send_batch_max_size: 2048

  # Resource processor to add metadata
  resource:
    attributes:
      - key: service.name
        value: secwalk
        action: upsert
      - key: service.version
        value: v1.0.0
        action: upsert
      - key: deployment.environment
        from_attribute: environment
        action: upsert
      - key: service.instance.id
        from_attribute: service.instance.id
        action: upsert

  # Memory limiter to prevent OOM
  memory_limiter:
    limit_mib: 512

  # Probabilistic sampler for traces (optional, can be configured per service)
  probabilistic_sampler:
    sampling_percentage: 10  # 10% sampling rate

exporters:
  # AWS X-Ray for distributed tracing
  awsxray:
    region: ${AWS_REGION}
    no_verify_ssl: false

  # AWS CloudWatch for metrics
  awscloudwatchmetrics:
    region: ${AWS_REGION}
    namespace: SecWalk/Application
    dimension_rollup_option: NoDimensionRollup
    metric_declarations:
      - dimensions: [[service.name], [service.name, service.version]]
        metric_name_selectors:
          - ".*"

  # CloudWatch Logs for application logs
  awscloudwatchlogs:
    region: ${AWS_REGION}
    log_group_name: /aws/secwalk/application
    log_stream_name: secwalk-{instance_id}

  # Logging exporter for debugging (可在生产环境中禁用)
  logging:
    loglevel: info

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, resource, batch]
      exporters: [awsxray, logging]
    
    metrics:
      receivers: [otlp, prometheus, hostmetrics]
      processors: [memory_limiter, resource, batch]
      exporters: [awscloudwatchmetrics, logging]
    
    logs:
      receivers: [otlp]
      processors: [memory_limiter, resource, batch]
      exporters: [awscloudwatchlogs, logging]

  extensions: []
  telemetry:
    logs:
      level: info
    metrics:
      address: 0.0.0.0:8888
