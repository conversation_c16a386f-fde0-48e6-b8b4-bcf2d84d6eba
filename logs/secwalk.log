{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: ************:9000","timestamp":"2025-06-25T11:42:23.360+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-25T11:42:23.365+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct ************:9994","timestamp":"2025-06-25T11:42:23.366+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct ************:8994","timestamp":"2025-06-25T11:42:23.466+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct 192.168.129.233:8995","timestamp":"2025-06-25T11:42:23.567+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct ************:18092","timestamp":"2025-06-25T11:42:23.669+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct ************:9996","timestamp":"2025-06-25T11:42:23.770+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-25T11:42:23.871+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-25T11:42:23.874+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-25T11:42:23.878+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-25T11:42:23.881+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T11:42:23.889+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-25T11:42:24.110+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-25T11:42:24.110+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-25T11:42:24.110+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-25T11:42:24.111+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-25T11:42:24.111+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-25T11:42:24.111+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-25T11:42:24.111+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-25T11:42:24.112+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-25T11:42:24.112+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-25T11:42:24.112+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-25T11:42:24.113+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-25T11:42:24.113+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-25T11:42:24.113+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-25T11:42:24.113+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-25T11:42:24.114+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-25T11:42:24.114+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-25T11:42:24.114+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-25T11:42:24.114+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-25T11:42:24.115+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-25T11:42:24.115+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-25T11:42:24.115+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-25T11:42:24.116+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-25T11:42:24.116+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-25T11:42:24.116+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-25T11:42:24.116+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-25T11:42:24.116+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-25T11:42:24.117+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-25T11:42:24.117+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-25T11:42:24.117+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-25T11:42:24.117+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-25T11:42:24.118+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-25T11:42:24.118+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-25T11:42:24.118+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-25T11:42:24.118+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-25T11:42:24.118+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-25T11:42:24.119+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-25T11:42:24.120+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-25T11:42:24.120+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-e2a89176-8d7c-40c0-b478-0f98f58746ca","timestamp":"2025-06-25T11:42:24.121+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: ************:9000","timestamp":"2025-06-25T11:43:04.824+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-25T11:43:04.828+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct 192.168.129.233:8995","timestamp":"2025-06-25T11:43:04.829+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct ************:18092","timestamp":"2025-06-25T11:43:04.929+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct ************:9996","timestamp":"2025-06-25T11:43:05.030+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct ************:9994","timestamp":"2025-06-25T11:43:05.132+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct ************:8994","timestamp":"2025-06-25T11:43:05.232+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-25T11:43:05.332+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-25T11:43:05.335+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-25T11:43:05.339+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-25T11:43:05.341+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T11:43:05.349+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-25T11:43:05.552+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-25T11:43:05.552+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-25T11:43:05.553+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-25T11:43:05.553+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-25T11:43:05.553+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-25T11:43:05.553+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-25T11:43:05.554+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-25T11:43:05.554+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-25T11:43:05.554+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-25T11:43:05.554+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-25T11:43:05.555+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-25T11:43:05.555+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-25T11:43:05.555+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-25T11:43:05.555+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-25T11:43:05.556+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-25T11:43:05.556+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-25T11:43:05.556+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-25T11:43:05.556+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-25T11:43:05.557+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-25T11:43:05.557+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-25T11:43:05.557+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-25T11:43:05.557+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-25T11:43:05.558+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-25T11:43:05.558+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-25T11:43:05.558+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-25T11:43:05.558+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-25T11:43:05.559+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-25T11:43:05.559+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-25T11:43:05.559+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-25T11:43:05.559+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-25T11:43:05.560+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-25T11:43:05.560+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-25T11:43:05.560+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-25T11:43:05.560+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-25T11:43:05.561+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-25T11:43:05.561+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-25T11:43:05.562+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-25T11:43:05.562+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-38ea21a5-58f4-45aa-8590-0b9d624eb421","timestamp":"2025-06-25T11:43:05.563+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/toolkit, ext: ","tid":"","timestamp":"2025-06-25T11:44:15.164+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:61161, status: 200","tid":"","timestamp":"2025-06-25T11:44:15.181+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T11:44:28.849+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T11:44:28.855+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T11:44:28.870+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T11:44:28.870+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T11:44:28.870+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T11:44:28.878+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T11:44:28.878+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T11:44:28.885+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T11:44:28.886+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T11:44:28.886+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T11:44:28.887+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node12] node run error: secvirt service not found","timestamp":"2025-06-25T11:44:28.887+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: secvirt service not found","tid":"","timestamp":"2025-06-25T11:44:28.887+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:61161, status: 200","tid":"","timestamp":"2025-06-25T11:44:28.887+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T11:48:14.158+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T11:49:01.087+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T11:49:01.093+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T11:49:01.171+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T11:49:01.171+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T11:49:01.171+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T11:49:01.230+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T11:49:01.231+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T11:49:01.245+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T11:49:01.246+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T11:49:01.248+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T11:49:01.250+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node12] node run error: secvirt service not found","timestamp":"2025-06-25T11:49:01.252+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: secvirt service not found","tid":"","timestamp":"2025-06-25T11:49:01.253+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:61161, status: 200","tid":"","timestamp":"2025-06-25T11:49:01.255+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: ************:9000","timestamp":"2025-06-25T11:49:31.683+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-25T11:49:31.687+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct ************:9996","timestamp":"2025-06-25T11:49:31.687+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct ************:9994","timestamp":"2025-06-25T11:49:31.787+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct ************:8994","timestamp":"2025-06-25T11:49:31.888+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct ************:8995","timestamp":"2025-06-25T11:49:31.989+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct ************:18092","timestamp":"2025-06-25T11:49:32.089+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-25T11:49:32.190+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-25T11:49:32.190+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-25T11:49:32.192+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-25T11:49:32.193+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T11:49:32.199+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-25T11:49:32.399+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-25T11:49:32.400+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-25T11:49:32.400+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-25T11:49:32.401+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-25T11:49:32.401+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-25T11:49:32.401+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-25T11:49:32.402+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-25T11:49:32.402+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-25T11:49:32.402+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-25T11:49:32.402+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-25T11:49:32.403+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-25T11:49:32.403+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-25T11:49:32.403+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-25T11:49:32.403+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-25T11:49:32.404+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-25T11:49:32.404+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-25T11:49:32.404+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-25T11:49:32.404+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-25T11:49:32.405+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-25T11:49:32.405+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-25T11:49:32.405+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-25T11:49:32.405+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-25T11:49:32.406+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-25T11:49:32.406+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-25T11:49:32.406+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-25T11:49:32.406+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-25T11:49:32.407+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-25T11:49:32.407+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-25T11:49:32.407+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-25T11:49:32.407+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-25T11:49:32.408+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-25T11:49:32.408+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-25T11:49:32.408+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-25T11:49:32.408+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-25T11:49:32.409+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-25T11:49:32.409+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-25T11:49:32.410+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-25T11:49:32.410+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-78aa772d-1cda-40de-af6d-09116cc57b56","timestamp":"2025-06-25T11:49:32.411+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T11:49:38.148+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T11:49:38.156+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T11:49:38.175+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T11:49:38.175+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T11:49:38.176+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T11:49:38.183+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T11:49:38.184+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T11:49:38.189+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T11:49:38.190+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T11:49:38.190+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T11:49:38.191+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node12] node run error: secvirt service not found","timestamp":"2025-06-25T11:49:38.191+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: secvirt service not found","tid":"","timestamp":"2025-06-25T11:49:38.191+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:61251, status: 200","tid":"","timestamp":"2025-06-25T11:49:38.192+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T11:54:42.588+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T11:59:53.024+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:05:04.866+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:10:16.747+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:15:30.141+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:20:43.234+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:25:56.038+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:31:10.467+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:36:21.720+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:41:36.173+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:46:49.141+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:52:01.945+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T12:57:16.313+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:02:29.528+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: ************:9000","timestamp":"2025-06-25T13:03:10.042+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-25T13:03:10.049+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct ************:9994","timestamp":"2025-06-25T13:03:10.051+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct 10.50.10.18:8994","timestamp":"2025-06-25T13:03:10.150+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct ************:8995","timestamp":"2025-06-25T13:03:10.251+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct ************:18092","timestamp":"2025-06-25T13:03:10.352+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct ************:9996","timestamp":"2025-06-25T13:03:10.452+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-25T13:03:10.553+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-25T13:03:10.554+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-25T13:03:10.558+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-25T13:03:10.561+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:03:10.572+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-25T13:03:10.750+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-25T13:03:10.750+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-25T13:03:10.750+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-25T13:03:10.751+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-25T13:03:10.751+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-25T13:03:10.751+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-25T13:03:10.751+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-25T13:03:10.752+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-25T13:03:10.752+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-25T13:03:10.752+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-25T13:03:10.752+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-25T13:03:10.752+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-25T13:03:10.753+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-25T13:03:10.753+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-25T13:03:10.753+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-25T13:03:10.753+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-25T13:03:10.754+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-25T13:03:10.754+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-25T13:03:10.754+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-25T13:03:10.754+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-25T13:03:10.755+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-25T13:03:10.755+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-25T13:03:10.755+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-25T13:03:10.755+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-25T13:03:10.756+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-25T13:03:10.756+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-25T13:03:10.756+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-25T13:03:10.756+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-25T13:03:10.756+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-25T13:03:10.757+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-25T13:03:10.757+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-25T13:03:10.757+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-25T13:03:10.757+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-25T13:03:10.758+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-25T13:03:10.758+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-25T13:03:10.758+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-25T13:03:10.759+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-25T13:03:10.760+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-5840dd20-086b-45b6-ae7b-7d3767d1a8be","timestamp":"2025-06-25T13:03:10.761+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:03:45.591+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:03:45.597+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:03:45.609+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:03:45.609+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:03:45.609+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:03:45.623+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:03:45.624+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:03:45.626+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:03:45.627+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:03:45.627+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:03:45.627+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node12] node run error: secvirt service not found","timestamp":"2025-06-25T13:03:45.628+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: secvirt service not found","tid":"","timestamp":"2025-06-25T13:03:45.628+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:61888, status: 200","tid":"","timestamp":"2025-06-25T13:03:45.628+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:05:12.341+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:05:12.346+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:05:12.360+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:05:12.360+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:05:12.360+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:05:12.368+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:05:12.368+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:05:12.371+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:05:12.371+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:05:12.372+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:05:21.151+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node12] node run error: secvirt service not found","timestamp":"2025-06-25T13:10:04.715+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: secvirt service not found","tid":"","timestamp":"2025-06-25T13:10:04.717+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:61888, status: 200","tid":"","timestamp":"2025-06-25T13:10:04.717+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:10:04.721+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:10:13.254+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:10:13.260+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:10:13.272+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:10:13.272+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:10:13.272+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:10:13.279+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:10:13.279+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:10:13.281+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:10:13.281+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:10:13.282+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:10:16.470+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: ************:9000","timestamp":"2025-06-25T13:11:40.542+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-25T13:11:40.549+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct ************:18092","timestamp":"2025-06-25T13:11:40.550+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct ************:9996","timestamp":"2025-06-25T13:11:40.650+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct ************:9994","timestamp":"2025-06-25T13:11:40.750+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct ************:8994","timestamp":"2025-06-25T13:11:40.850+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct ************:8995","timestamp":"2025-06-25T13:11:40.951+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-25T13:11:41.051+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-25T13:11:41.051+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-25T13:11:41.052+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-25T13:11:41.053+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:11:41.062+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-25T13:11:41.281+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-25T13:11:41.282+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-25T13:11:41.282+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-25T13:11:41.282+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-25T13:11:41.282+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-25T13:11:41.282+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-25T13:11:41.282+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-25T13:11:41.282+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-25T13:11:41.282+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-25T13:11:41.283+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-25T13:11:41.283+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-25T13:11:41.283+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-25T13:11:41.283+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-25T13:11:41.283+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-25T13:11:41.283+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-25T13:11:41.283+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-25T13:11:41.283+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-25T13:11:41.283+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-25T13:11:41.284+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-25T13:11:41.284+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-25T13:11:41.284+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-25T13:11:41.284+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-25T13:11:41.284+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-25T13:11:41.284+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-25T13:11:41.284+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-25T13:11:41.284+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-25T13:11:41.284+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-25T13:11:41.285+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-25T13:11:41.285+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-25T13:11:41.285+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-25T13:11:41.285+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-25T13:11:41.285+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-25T13:11:41.285+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-25T13:11:41.285+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-25T13:11:41.285+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-25T13:11:41.285+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-25T13:11:41.286+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-25T13:11:41.286+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-91822242-0094-43a7-bb0c-c59f65246776","timestamp":"2025-06-25T13:11:41.287+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:11:47.463+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:11:47.469+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:11:47.480+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:11:47.481+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:11:47.481+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:11:47.488+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:11:47.488+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:11:47.494+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:11:47.495+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:11:47.495+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:11:47.495+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: ************:9000","timestamp":"2025-06-25T13:13:32.335+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-25T13:13:32.338+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct ************:8994","timestamp":"2025-06-25T13:13:32.339+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct ************:8995","timestamp":"2025-06-25T13:13:32.439+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct ************:18092","timestamp":"2025-06-25T13:13:32.539+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct ************:9996","timestamp":"2025-06-25T13:13:32.640+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct ************:9994","timestamp":"2025-06-25T13:13:32.741+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-25T13:13:32.842+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-25T13:13:32.844+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-25T13:13:32.851+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-25T13:13:32.854+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:13:32.863+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-25T13:13:33.052+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-25T13:13:33.053+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-25T13:13:33.053+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-25T13:13:33.053+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-25T13:13:33.053+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-25T13:13:33.054+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-25T13:13:33.054+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-25T13:13:33.054+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-25T13:13:33.054+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-25T13:13:33.054+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-25T13:13:33.055+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-25T13:13:33.055+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-25T13:13:33.055+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-25T13:13:33.055+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-25T13:13:33.056+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-25T13:13:33.056+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-25T13:13:33.056+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-25T13:13:33.056+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-25T13:13:33.057+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-25T13:13:33.057+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-25T13:13:33.057+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-25T13:13:33.057+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-25T13:13:33.057+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-25T13:13:33.058+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-25T13:13:33.058+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-25T13:13:33.058+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-25T13:13:33.059+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-25T13:13:33.059+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-25T13:13:33.059+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-25T13:13:33.059+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-25T13:13:33.059+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-25T13:13:33.060+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-25T13:13:33.060+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-25T13:13:33.060+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-25T13:13:33.060+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-25T13:13:33.060+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-25T13:13:33.061+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-25T13:13:33.061+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-94391471-690a-4839-882d-3c9d3305cf06","timestamp":"2025-06-25T13:13:33.062+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:13:35.998+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:13:36.005+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:13:36.020+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:13:36.020+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:13:36.021+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:13:36.026+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:13:36.027+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:13:36.032+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:13:36.032+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:13:36.032+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:13:36.033+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: ************:9000","timestamp":"2025-06-25T13:14:29.244+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-25T13:14:29.246+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct ************:9994","timestamp":"2025-06-25T13:14:29.247+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct ************:8994","timestamp":"2025-06-25T13:14:29.347+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct ************:8995","timestamp":"2025-06-25T13:14:29.448+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct ************:18092","timestamp":"2025-06-25T13:14:29.550+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct ************:9996","timestamp":"2025-06-25T13:14:29.651+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-25T13:14:29.751+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-25T13:14:29.751+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-25T13:14:29.752+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-25T13:14:29.753+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:14:29.758+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-25T13:14:29.925+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-25T13:14:29.926+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-25T13:14:29.926+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-25T13:14:29.926+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-25T13:14:29.927+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-25T13:14:29.927+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-25T13:14:29.927+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-25T13:14:29.933+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-25T13:14:29.933+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-25T13:14:29.933+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-25T13:14:29.934+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-25T13:14:29.934+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-25T13:14:29.934+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-25T13:14:29.935+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-25T13:14:29.935+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-25T13:14:29.935+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-25T13:14:29.935+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-25T13:14:29.936+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-25T13:14:29.936+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-25T13:14:29.936+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-25T13:14:29.937+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-25T13:14:29.937+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-25T13:14:29.937+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-25T13:14:29.938+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-25T13:14:29.938+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-25T13:14:29.938+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-25T13:14:29.938+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-25T13:14:29.939+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-25T13:14:29.939+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-25T13:14:29.939+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-25T13:14:29.939+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-25T13:14:29.939+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-25T13:14:29.940+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-25T13:14:29.940+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-25T13:14:29.940+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-25T13:14:29.940+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-25T13:14:29.941+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-25T13:14:29.942+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-a8811b50-6424-491e-9128-5aca2d94a413","timestamp":"2025-06-25T13:14:29.942+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:14:32.687+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:14:32.696+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:14:32.707+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:14:32.707+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:14:32.707+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:14:32.713+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:14:32.713+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:14:32.717+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:14:32.718+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:14:32.718+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:14:32.718+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node12] code interpreter start","timestamp":"2025-06-25T13:14:47.726+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:100","level":"ERROR","module":"codeide","msg":"[node12] code interpreter error: [{\"name\":\"BadRequestError\",\"traceback\":\"---------------------------------------------------------------------------\\nBadRequestError                           Traceback (most recent call last)\\nCell In[1], line 35\\n     32     # 函数执行\\n     33     return mod.handler(args)\\n---\u003e 35 handler()\\n\\nCell In[1], line 33, in handler()\\n     30 args = Args(inputs)\\n     32 # 函数执行\\n---\u003e 33 return mod.handler(args)\\n\\nFile \u003cstring\u003e:9, in handler(args)\\n\\nFile /usr/local/lib/python3.10/site-packages/runtime/file.py:44, in FileBucket.get(self, name)\\n     42             raise UnauthorizedError\\n     43         elif res.status_code == 400:\\n---\u003e 44             raise BadRequestError\\n     45         return b''\\n     46 except requests.RequestException as e:\\n\\nBadRequestError: \",\"type\":\"error\"}]","timestamp":"2025-06-25T13:14:51.749+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:19:45.586+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node12] node run error: [{\"name\":\"BadRequestError\",\"traceback\":\"---------------------------------------------------------------------------\\nBadRequestError                           Traceback (most recent call last)\\nCell In[1], line 35\\n     32     # 函数执行\\n     33     return mod.handler(args)\\n---\u003e 35 handler()\\n\\nCell In[1], line 33, in handler()\\n     30 args = Args(inputs)\\n     32 # 函数执行\\n---\u003e 33 return mod.handler(args)\\n\\nFile \u003cstring\u003e:9, in handler(args)\\n\\nFile /usr/local/lib/python3.10/site-packages/runtime/file.py:44, in FileBucket.get(self, name)\\n     42             raise UnauthorizedError\\n     43         elif res.status_code == 400:\\n---\u003e 44             raise BadRequestError\\n     45         return b''\\n     46 except requests.RequestException as e:\\n\\nBadRequestError: \",\"type\":\"error\"}]","timestamp":"2025-06-25T13:19:45.587+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: [{\"name\":\"BadRequestError\",\"traceback\":\"---------------------------------------------------------------------------\\nBadRequestError                           Traceback (most recent call last)\\nCell In[1], line 35\\n     32     # 函数执行\\n     33     return mod.handler(args)\\n---\u003e 35 handler()\\n\\nCell In[1], line 33, in handler()\\n     30 args = Args(inputs)\\n     32 # 函数执行\\n---\u003e 33 return mod.handler(args)\\n\\nFile \u003cstring\u003e:9, in handler(args)\\n\\nFile /usr/local/lib/python3.10/site-packages/runtime/file.py:44, in FileBucket.get(self, name)\\n     42             raise UnauthorizedError\\n     43         elif res.status_code == 400:\\n---\u003e 44             raise BadRequestError\\n     45         return b''\\n     46 except requests.RequestException as e:\\n\\nBadRequestError: \",\"type\":\"error\"}]","tid":"","timestamp":"2025-06-25T13:19:45.588+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:62105, status: 200","tid":"","timestamp":"2025-06-25T13:19:45.589+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:19:45.589+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:19:45.594+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:19:45.608+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:19:45.608+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:19:45.608+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:19:45.616+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:19:45.616+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:19:45.620+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node5] node running, type: llm_mixup, node_name: 大模型_fED3Ou","timestamp":"2025-06-25T13:19:45.620+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node5] get node inputs","timestamp":"2025-06-25T13:19:45.621+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node5] get node inputs done","timestamp":"2025-06-25T13:19:45.621+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:53","level":"INFO","module":"llm","msg":"[node5] chat llm request: {\"model\":\"HengNao-r1\",\"max_tokens\":4096,\"temperature\":0,\"top_k\":-1,\"top_p\":1,\"repetition_penalty\":0,\"reasoning\":true}","timestamp":"2025-06-25T13:19:48.159+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:68","level":"INFO","module":"llm","msg":"[node5] chat llm response: id:chatcmpl-d8150e1af2e142d0a30d7e69e9eb45c5, token_usage:3024 2598 8136, reason:stop","timestamp":"2025-06-25T13:22:03.439+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node5] parse node output","timestamp":"2025-06-25T13:22:03.440+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues_1","timestamp":"2025-06-25T13:22:03.441+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues","timestamp":"2025-06-25T13:22:03.442+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node6] node running, type: act_tool, node_name: 插件_2xnhgp","timestamp":"2025-06-25T13:22:03.443+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:22:03.443+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:22:03.445+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:22:03.445+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:22:03.468+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:24:27.852+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:24:27.854+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:24:27.855+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 处理逻辑错误","timestamp":"2025-06-25T13:24:27.855+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:24:27.856+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:24:27.856+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:24:27.857+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"处理逻辑错误\"}","timestamp":"2025-06-25T13:24:27.858+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:25:06.117+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:25:14.399+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:25:14.399+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:25:14.400+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:25:14.401+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 1","timestamp":"2025-06-25T13:26:09.808+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:26:16.380+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:213","level":"INFO","module":"agent","msg":"[node6] iter done","timestamp":"2025-06-25T13:26:16.381+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node7] node running, type: act_interpreter, node_name: 代码_YJ9nCb","timestamp":"2025-06-25T13:26:16.381+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node7] get node inputs","timestamp":"2025-06-25T13:26:16.381+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node7] get node inputs done","timestamp":"2025-06-25T13:26:16.382+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node7] code interpreter start","timestamp":"2025-06-25T13:26:31.156+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node7] node run error: code execute failed","timestamp":"2025-06-25T13:26:31.157+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: code execute failed","tid":"","timestamp":"2025-06-25T13:26:31.157+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:62198, status: 200","tid":"","timestamp":"2025-06-25T13:26:31.158+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:26:42.827+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:26:42.832+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:26:42.846+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:26:42.846+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:26:42.846+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:26:42.853+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:26:42.853+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:26:42.856+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node5] node running, type: llm_mixup, node_name: 大模型_fED3Ou","timestamp":"2025-06-25T13:26:42.856+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node5] get node inputs","timestamp":"2025-06-25T13:26:42.856+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node5] get node inputs done","timestamp":"2025-06-25T13:26:42.857+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:53","level":"INFO","module":"llm","msg":"[node5] chat llm request: {\"model\":\"HengNao-r1\",\"max_tokens\":4096,\"temperature\":0,\"top_k\":-1,\"top_p\":1,\"repetition_penalty\":0,\"reasoning\":true}","timestamp":"2025-06-25T13:26:42.867+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:68","level":"INFO","module":"llm","msg":"[node5] chat llm response: id:chatcmpl-424e42fb0d064cc28661d2de66ec6dee, token_usage:3024 1229 5356, reason:stop","timestamp":"2025-06-25T13:27:46.393+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node5] parse node output","timestamp":"2025-06-25T13:27:46.395+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues_1","timestamp":"2025-06-25T13:27:46.397+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues","timestamp":"2025-06-25T13:27:46.398+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node6] node running, type: act_tool, node_name: 插件_2xnhgp","timestamp":"2025-06-25T13:27:46.400+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:27:46.401+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:27:46.403+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:27:46.403+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:27:46.416+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:27:46.416+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:27:46.416+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:27:46.417+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 资源管理错误","timestamp":"2025-06-25T13:27:46.417+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:27:46.417+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:27:46.417+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:27:46.417+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"资源管理错误\"}","timestamp":"2025-06-25T13:27:46.418+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:27:46.431+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:27:46.431+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:27:46.431+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:27:46.432+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 1","timestamp":"2025-06-25T13:29:07.110+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:29:07.111+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:213","level":"INFO","module":"agent","msg":"[node6] iter done","timestamp":"2025-06-25T13:29:07.112+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node7] node running, type: act_interpreter, node_name: 代码_YJ9nCb","timestamp":"2025-06-25T13:29:07.113+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node7] get node inputs","timestamp":"2025-06-25T13:29:07.113+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node7] get node inputs done","timestamp":"2025-06-25T13:29:07.115+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node7] code interpreter start","timestamp":"2025-06-25T13:29:07.116+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:100","level":"ERROR","module":"codeide","msg":"[node7] code interpreter error: [{\"name\":\"KeyError\",\"traceback\":\"---------------------------------------------------------------------------\\nKeyError                                  Traceback (most recent call last)\\nCell In[1], line 35\\n     32     # 函数执行\\n     33     return mod.handler(args)\\n---\u003e 35 handler()\\n\\nCell In[1], line 33, in handler()\\n     30 args = Args(inputs)\\n     32 # 函数执行\\n---\u003e 33 return mod.handler(args)\\n\\nFile \u003cstring\u003e:13, in handler(args)\\n\\nKeyError: 'mixRetrieveResults'\",\"type\":\"error\",\"value\":\"'mixRetrieveResults'\"}]","timestamp":"2025-06-25T13:29:11.147+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node7] node run error: [{\"name\":\"KeyError\",\"traceback\":\"---------------------------------------------------------------------------\\nKeyError                                  Traceback (most recent call last)\\nCell In[1], line 35\\n     32     # 函数执行\\n     33     return mod.handler(args)\\n---\u003e 35 handler()\\n\\nCell In[1], line 33, in handler()\\n     30 args = Args(inputs)\\n     32 # 函数执行\\n---\u003e 33 return mod.handler(args)\\n\\nFile \u003cstring\u003e:13, in handler(args)\\n\\nKeyError: 'mixRetrieveResults'\",\"type\":\"error\",\"value\":\"'mixRetrieveResults'\"}]","timestamp":"2025-06-25T13:29:11.148+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: [{\"name\":\"KeyError\",\"traceback\":\"---------------------------------------------------------------------------\\nKeyError                                  Traceback (most recent call last)\\nCell In[1], line 35\\n     32     # 函数执行\\n     33     return mod.handler(args)\\n---\u003e 35 handler()\\n\\nCell In[1], line 33, in handler()\\n     30 args = Args(inputs)\\n     32 # 函数执行\\n---\u003e 33 return mod.handler(args)\\n\\nFile \u003cstring\u003e:13, in handler(args)\\n\\nKeyError: 'mixRetrieveResults'\",\"type\":\"error\",\"value\":\"'mixRetrieveResults'\"}]","tid":"","timestamp":"2025-06-25T13:29:11.148+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:62292, status: 200","tid":"","timestamp":"2025-06-25T13:29:11.149+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:29:19.335+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:29:19.340+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:29:19.354+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:29:19.354+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:29:19.355+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:29:19.360+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:29:19.361+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:29:19.365+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node5] node running, type: llm_mixup, node_name: 大模型_fED3Ou","timestamp":"2025-06-25T13:29:19.365+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node5] get node inputs","timestamp":"2025-06-25T13:29:19.366+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node5] get node inputs done","timestamp":"2025-06-25T13:29:19.366+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:53","level":"INFO","module":"llm","msg":"[node5] chat llm request: {\"model\":\"HengNao-r1\",\"max_tokens\":4096,\"temperature\":0,\"top_k\":-1,\"top_p\":1,\"repetition_penalty\":0,\"reasoning\":true}","timestamp":"2025-06-25T13:29:19.373+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:30:19.411+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:68","level":"INFO","module":"llm","msg":"[node5] chat llm response: id:chatcmpl-beee45024ffc48e1a68b3c224a79c4ee, token_usage:3024 1229 5356, reason:stop","timestamp":"2025-06-25T13:30:21.345+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node5] parse node output","timestamp":"2025-06-25T13:30:21.346+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues_1","timestamp":"2025-06-25T13:30:21.347+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues","timestamp":"2025-06-25T13:30:21.347+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node6] node running, type: act_tool, node_name: 插件_2xnhgp","timestamp":"2025-06-25T13:30:21.347+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:30:21.347+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:30:21.348+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:30:21.348+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:30:21.374+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:30:21.374+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:30:21.375+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:30:21.375+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 资源管理错误","timestamp":"2025-06-25T13:30:21.375+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:30:21.375+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:30:21.375+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:30:21.376+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"资源管理错误\"}","timestamp":"2025-06-25T13:30:21.376+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:30:21.386+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:30:21.386+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:30:21.386+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:30:21.386+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 1","timestamp":"2025-06-25T13:32:33.264+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:32:39.124+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: ************:9000","timestamp":"2025-06-25T13:35:13.002+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-25T13:35:13.005+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct ************:9996","timestamp":"2025-06-25T13:35:13.005+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct ************:9994","timestamp":"2025-06-25T13:35:13.105+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct ************:8994","timestamp":"2025-06-25T13:35:13.205+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct ************:8995","timestamp":"2025-06-25T13:35:13.307+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct ************:18092","timestamp":"2025-06-25T13:35:13.407+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-25T13:35:13.507+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-25T13:35:13.507+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-25T13:35:13.509+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-25T13:35:13.510+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:35:13.514+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-25T13:35:40.431+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-25T13:35:40.431+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-25T13:35:40.432+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-25T13:35:40.432+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-25T13:35:40.432+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-25T13:35:40.433+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-25T13:35:40.433+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-25T13:35:40.433+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-25T13:35:40.433+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-25T13:35:40.434+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-25T13:35:40.434+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-25T13:35:40.434+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-25T13:35:40.434+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-25T13:35:40.434+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-25T13:35:40.434+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-25T13:35:40.435+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-25T13:35:40.435+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-25T13:35:40.435+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-25T13:35:40.435+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-25T13:35:40.435+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-25T13:35:40.436+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-25T13:35:40.436+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-25T13:35:40.436+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-25T13:35:40.436+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-25T13:35:40.436+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-25T13:35:40.436+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-25T13:35:40.437+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-25T13:35:40.437+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-25T13:35:40.437+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-25T13:35:40.437+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-25T13:35:40.437+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-25T13:35:40.437+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-25T13:35:40.438+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-25T13:35:40.438+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-25T13:35:40.438+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-25T13:35:40.438+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-25T13:35:40.439+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-25T13:35:40.440+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-34d39670-3c30-4c51-94f5-68da694c0c84","timestamp":"2025-06-25T13:35:40.440+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:37:16.534+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:37:16.540+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:37:16.555+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:37:16.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:37:16.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:37:16.564+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:37:16.565+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:37:16.569+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:37:16.570+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:37:16.570+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:37:16.571+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node12] code interpreter start","timestamp":"2025-06-25T13:37:41.763+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: ************:9000","timestamp":"2025-06-25T13:38:28.053+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-25T13:38:28.055+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct ************:9994","timestamp":"2025-06-25T13:38:28.055+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct ************:8994","timestamp":"2025-06-25T13:38:28.156+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct ************:8995","timestamp":"2025-06-25T13:38:28.256+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct ************:18092","timestamp":"2025-06-25T13:38:28.358+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct ************:9996","timestamp":"2025-06-25T13:38:28.458+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-25T13:38:28.558+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-25T13:38:28.559+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-25T13:38:28.560+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-25T13:38:28.562+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:38:28.567+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-25T13:38:28.741+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-25T13:38:28.741+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-25T13:38:28.741+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-25T13:38:28.742+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-25T13:38:28.742+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-25T13:38:28.742+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-25T13:38:28.742+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-25T13:38:28.743+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-25T13:38:28.743+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-25T13:38:28.743+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-25T13:38:28.743+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-25T13:38:28.743+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-25T13:38:28.744+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-25T13:38:28.744+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-25T13:38:28.744+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-25T13:38:28.744+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-25T13:38:28.745+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-25T13:38:28.745+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-25T13:38:28.745+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-25T13:38:28.745+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-25T13:38:28.746+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-25T13:38:28.746+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-25T13:38:28.746+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-25T13:38:28.746+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-25T13:38:28.747+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-25T13:38:28.747+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-25T13:38:28.747+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-25T13:38:28.747+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-25T13:38:28.748+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-25T13:38:28.748+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-25T13:38:28.748+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-25T13:38:28.748+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-25T13:38:28.748+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-25T13:38:28.748+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-25T13:38:28.749+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-25T13:38:28.749+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-25T13:38:28.750+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-25T13:38:28.750+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-8f300c89-5f91-493e-beac-af8743d0896d","timestamp":"2025-06-25T13:38:28.751+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:38:31.390+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:38:31.397+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:38:31.411+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:38:31.411+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:38:31.412+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:38:31.419+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:38:31.420+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:38:31.424+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:38:31.425+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:38:31.425+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:38:31.425+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node12] code interpreter start","timestamp":"2025-06-25T13:38:38.544+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node12] parse node output","timestamp":"2025-06-25T13:38:58.292+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node5] node running, type: llm_mixup, node_name: 大模型_fED3Ou","timestamp":"2025-06-25T13:38:58.293+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node5] get node inputs","timestamp":"2025-06-25T13:38:58.293+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node5] get node inputs done","timestamp":"2025-06-25T13:38:58.294+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:53","level":"INFO","module":"llm","msg":"[node5] chat llm request: {\"model\":\"HengNao-r1\",\"max_tokens\":4096,\"temperature\":0,\"top_k\":-1,\"top_p\":1,\"repetition_penalty\":0,\"reasoning\":true}","timestamp":"2025-06-25T13:38:58.304+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:68","level":"INFO","module":"llm","msg":"[node5] chat llm response: id:chatcmpl-a7e461a2b0d14c20aa17f4d3b97ec1be, token_usage:3388 1669 6438, reason:stop","timestamp":"2025-06-25T13:40:23.879+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node5] parse node output","timestamp":"2025-06-25T13:40:23.880+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues_1","timestamp":"2025-06-25T13:40:23.882+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues","timestamp":"2025-06-25T13:40:23.882+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node6] node running, type: act_tool, node_name: 插件_2xnhgp","timestamp":"2025-06-25T13:40:23.882+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:40:23.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:40:23.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:40:23.884+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:40:23.914+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:40:23.914+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:40:23.915+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:40:23.915+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 信任管理问题","timestamp":"2025-06-25T13:40:23.915+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:40:23.915+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:40:23.916+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:40:23.916+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"信任管理问题\"}","timestamp":"2025-06-25T13:40:23.916+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:40:23.929+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:40:23.930+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:40:23.930+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:40:23.930+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 1","timestamp":"2025-06-25T13:40:29.138+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:40:35.350+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:40:35.350+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:40:40.334+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:40:40.356+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:40:40.356+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:40:40.357+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:40:40.357+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 权限许可和访问控制问题","timestamp":"2025-06-25T13:40:40.358+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:40:40.358+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:40:40.358+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:40:40.358+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"权限许可和访问控制问题\"}","timestamp":"2025-06-25T13:40:40.358+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:40:40.371+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:40:40.371+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:40:40.371+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:40:40.371+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 2","timestamp":"2025-06-25T13:40:42.532+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:40:42.533+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:40:42.533+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:40:42.534+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:40:42.555+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:40:42.555+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:40:42.555+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:40:42.555+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 处理逻辑错误","timestamp":"2025-06-25T13:40:42.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:40:42.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:40:42.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:40:42.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"处理逻辑错误\"}","timestamp":"2025-06-25T13:40:42.557+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:40:42.569+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:40:42.569+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:40:42.569+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:40:42.570+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 3","timestamp":"2025-06-25T13:40:43.297+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:40:43.298+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:40:43.298+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:40:43.299+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:40:43.323+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:40:43.323+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:40:43.324+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:40:43.324+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, API接口风险","timestamp":"2025-06-25T13:40:43.324+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:40:43.325+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:40:43.325+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:40:43.325+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"API接口风险\"}","timestamp":"2025-06-25T13:40:43.325+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:40:43.339+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:40:43.340+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:40:43.340+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:40:43.340+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 4","timestamp":"2025-06-25T13:40:45.102+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:40:45.102+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:213","level":"INFO","module":"agent","msg":"[node6] iter done","timestamp":"2025-06-25T13:40:45.103+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node7] node running, type: act_interpreter, node_name: 代码_YJ9nCb","timestamp":"2025-06-25T13:40:45.103+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node7] get node inputs","timestamp":"2025-06-25T13:40:45.104+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node7] get node inputs done","timestamp":"2025-06-25T13:40:45.104+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node7] code interpreter start","timestamp":"2025-06-25T13:40:47.423+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:100","level":"ERROR","module":"codeide","msg":"[node7] code interpreter error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-ff6ced7e-4062-4505-bb85-91ea343b5cdb/a0c8c029_11cc_4e81_8dc7_02c5d5959fdc.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","timestamp":"2025-06-25T13:40:47.615+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node7] node run error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-ff6ced7e-4062-4505-bb85-91ea343b5cdb/a0c8c029_11cc_4e81_8dc7_02c5d5959fdc.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","timestamp":"2025-06-25T13:40:47.617+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-ff6ced7e-4062-4505-bb85-91ea343b5cdb/a0c8c029_11cc_4e81_8dc7_02c5d5959fdc.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","tid":"","timestamp":"2025-06-25T13:40:47.618+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:62500, status: 200","tid":"","timestamp":"2025-06-25T13:40:47.618+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:41:02.197+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:41:02.202+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:41:02.213+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:41:02.213+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:41:02.213+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:41:02.218+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:41:02.218+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:41:02.222+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:41:02.222+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:41:02.222+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:41:02.223+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node12] code interpreter start","timestamp":"2025-06-25T13:41:51.662+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node12] parse node output","timestamp":"2025-06-25T13:41:51.873+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node5] node running, type: llm_mixup, node_name: 大模型_fED3Ou","timestamp":"2025-06-25T13:41:51.873+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node5] get node inputs","timestamp":"2025-06-25T13:41:51.874+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node5] get node inputs done","timestamp":"2025-06-25T13:41:51.874+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:53","level":"INFO","module":"llm","msg":"[node5] chat llm request: {\"model\":\"HengNao-r1\",\"max_tokens\":4096,\"temperature\":0,\"top_k\":-1,\"top_p\":1,\"repetition_penalty\":0,\"reasoning\":true}","timestamp":"2025-06-25T13:41:51.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:68","level":"INFO","module":"llm","msg":"[node5] chat llm response: id:chatcmpl-07f23b5a57464b92af445be4a371dc65, token_usage:3388 1669 6438, reason:stop","timestamp":"2025-06-25T13:43:17.830+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node5] parse node output","timestamp":"2025-06-25T13:43:17.831+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues_1","timestamp":"2025-06-25T13:43:17.832+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues","timestamp":"2025-06-25T13:43:17.834+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node6] node running, type: act_tool, node_name: 插件_2xnhgp","timestamp":"2025-06-25T13:43:17.835+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:43:17.836+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:43:17.836+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:43:17.836+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:43:17.860+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:43:17.860+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:43:17.861+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:43:17.861+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 信任管理问题","timestamp":"2025-06-25T13:43:17.861+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:43:17.862+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:43:17.862+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:43:17.862+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"信任管理问题\"}","timestamp":"2025-06-25T13:43:17.863+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:43:17.876+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:43:17.876+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:43:17.876+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:43:17.877+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 1","timestamp":"2025-06-25T13:43:57.494+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:43:57.497+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:44:01.554+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:44:01.554+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:44:16.538+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:44:16.555+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:44:16.555+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:44:16.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:44:16.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 权限许可和访问控制问题","timestamp":"2025-06-25T13:44:16.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:44:16.556+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:44:16.557+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:44:16.557+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"权限许可和访问控制问题\"}","timestamp":"2025-06-25T13:44:16.557+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:44:16.571+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:44:16.571+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:44:16.572+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:44:23.978+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 2","timestamp":"2025-06-25T13:45:05.354+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:45:17.872+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:45:17.873+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:49:40.341+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:49:40.364+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 3","timestamp":"2025-06-25T13:49:42.923+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:49:42.923+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:49:42.924+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:49:42.925+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 4","timestamp":"2025-06-25T13:49:44.205+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:49:44.205+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:213","level":"INFO","module":"agent","msg":"[node6] iter done","timestamp":"2025-06-25T13:49:44.206+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node7] node running, type: act_interpreter, node_name: 代码_YJ9nCb","timestamp":"2025-06-25T13:49:44.207+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node7] get node inputs","timestamp":"2025-06-25T13:49:44.207+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node7] get node inputs done","timestamp":"2025-06-25T13:49:44.208+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node7] code interpreter start","timestamp":"2025-06-25T13:49:45.029+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node7] node run error: code execute failed","timestamp":"2025-06-25T13:49:45.035+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: code execute failed","tid":"","timestamp":"2025-06-25T13:49:45.036+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:62500, status: 200","tid":"","timestamp":"2025-06-25T13:49:45.036+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T13:49:50.725+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T13:49:50.730+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T13:49:50.744+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T13:49:50.744+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T13:49:50.744+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T13:49:50.751+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T13:49:50.752+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T13:49:50.756+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T13:49:50.756+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T13:49:50.756+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T13:49:50.757+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node12] code interpreter start","timestamp":"2025-06-25T13:49:53.795+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node12] parse node output","timestamp":"2025-06-25T13:49:53.997+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node5] node running, type: llm_mixup, node_name: 大模型_fED3Ou","timestamp":"2025-06-25T13:49:53.998+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node5] get node inputs","timestamp":"2025-06-25T13:49:53.998+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node5] get node inputs done","timestamp":"2025-06-25T13:49:53.999+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:53","level":"INFO","module":"llm","msg":"[node5] chat llm request: {\"model\":\"HengNao-r1\",\"max_tokens\":4096,\"temperature\":0,\"top_k\":-1,\"top_p\":1,\"repetition_penalty\":0,\"reasoning\":true}","timestamp":"2025-06-25T13:49:54.010+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:68","level":"INFO","module":"llm","msg":"[node5] chat llm response: id:chatcmpl-bf5f088fb4e344cd9883036df59bef4b, token_usage:3388 1669 6438, reason:stop","timestamp":"2025-06-25T13:51:20.643+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node5] parse node output","timestamp":"2025-06-25T13:51:20.645+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues_1","timestamp":"2025-06-25T13:51:20.647+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues","timestamp":"2025-06-25T13:51:20.648+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node6] node running, type: act_tool, node_name: 插件_2xnhgp","timestamp":"2025-06-25T13:51:20.649+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:51:20.650+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:51:20.651+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:51:20.651+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:51:20.683+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:51:20.684+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:51:20.684+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:51:20.684+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 信任管理问题","timestamp":"2025-06-25T13:51:20.684+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:51:20.685+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:51:20.685+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:51:20.685+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"信任管理问题\"}","timestamp":"2025-06-25T13:51:20.685+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:51:20.701+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:51:20.701+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:51:20.702+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:51:20.702+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 1","timestamp":"2025-06-25T13:53:49.863+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:53:49.864+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:53:49.865+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:53:49.867+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:53:49.882+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:53:49.882+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:53:49.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:53:49.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 权限许可和访问控制问题","timestamp":"2025-06-25T13:53:49.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:53:49.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:53:49.884+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:53:49.884+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"权限许可和访问控制问题\"}","timestamp":"2025-06-25T13:53:49.884+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:53:49.896+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:53:49.896+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:53:49.897+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:53:49.897+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 2","timestamp":"2025-06-25T13:53:58.159+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:53:58.159+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:53:58.160+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:53:58.161+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:53:58.179+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:53:58.179+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:53:58.180+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:53:58.180+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 处理逻辑错误","timestamp":"2025-06-25T13:53:58.180+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:53:58.181+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:53:58.181+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:53:58.181+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"处理逻辑错误\"}","timestamp":"2025-06-25T13:53:58.182+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:53:58.194+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:53:58.195+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:53:58.195+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:53:58.195+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 3","timestamp":"2025-06-25T13:53:58.855+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:53:58.856+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T13:53:58.856+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T13:53:58.856+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:53:58.884+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T13:53:58.885+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T13:53:58.885+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T13:53:58.885+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, API接口风险","timestamp":"2025-06-25T13:53:58.885+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T13:53:58.886+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T13:53:58.886+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T13:53:58.886+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"API接口风险\"}","timestamp":"2025-06-25T13:53:58.886+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T13:53:58.899+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T13:53:58.899+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T13:53:58.900+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T13:53:58.900+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 4","timestamp":"2025-06-25T13:54:01.273+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T13:54:01.275+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:213","level":"INFO","module":"agent","msg":"[node6] iter done","timestamp":"2025-06-25T13:54:01.278+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node7] node running, type: act_interpreter, node_name: 代码_YJ9nCb","timestamp":"2025-06-25T13:54:01.279+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node7] get node inputs","timestamp":"2025-06-25T13:54:01.279+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node7] get node inputs done","timestamp":"2025-06-25T13:54:01.280+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node7] code interpreter start","timestamp":"2025-06-25T13:54:04.440+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:100","level":"ERROR","module":"codeide","msg":"[node7] code interpreter error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-89d683ef-73de-401a-bb38-797afbf56776/b382a72e_10c9_4058_82a4_a35b09578bbb.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","timestamp":"2025-06-25T13:54:04.627+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node7] node run error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-89d683ef-73de-401a-bb38-797afbf56776/b382a72e_10c9_4058_82a4_a35b09578bbb.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","timestamp":"2025-06-25T13:54:04.628+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-89d683ef-73de-401a-bb38-797afbf56776/b382a72e_10c9_4058_82a4_a35b09578bbb.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","tid":"","timestamp":"2025-06-25T13:54:04.628+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:62638, status: 200","tid":"","timestamp":"2025-06-25T13:54:04.628+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:54:49.519+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T13:59:58.089+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T14:00:07.886+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T14:00:07.891+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T14:00:07.906+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T14:00:07.907+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T14:00:07.907+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T14:00:07.914+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T14:00:07.915+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T14:00:07.920+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T14:00:07.920+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T14:00:07.921+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T14:00:07.921+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node12] code interpreter start","timestamp":"2025-06-25T14:00:10.249+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node12] parse node output","timestamp":"2025-06-25T14:00:10.494+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node5] node running, type: llm_mixup, node_name: 大模型_fED3Ou","timestamp":"2025-06-25T14:00:10.496+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node5] get node inputs","timestamp":"2025-06-25T14:00:10.497+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node5] get node inputs done","timestamp":"2025-06-25T14:00:10.498+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:53","level":"INFO","module":"llm","msg":"[node5] chat llm request: {\"model\":\"HengNao-r1\",\"max_tokens\":4096,\"temperature\":0,\"top_k\":-1,\"top_p\":1,\"repetition_penalty\":0,\"reasoning\":true}","timestamp":"2025-06-25T14:00:10.509+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:68","level":"INFO","module":"llm","msg":"[node5] chat llm response: id:chatcmpl-3b5829dcdeab4b33ad9e268b5c1dabf4, token_usage:3388 1669 6438, reason:stop","timestamp":"2025-06-25T14:01:35.802+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node5] parse node output","timestamp":"2025-06-25T14:01:35.803+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues_1","timestamp":"2025-06-25T14:01:35.804+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues","timestamp":"2025-06-25T14:01:35.804+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node6] node running, type: act_tool, node_name: 插件_2xnhgp","timestamp":"2025-06-25T14:01:35.805+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:01:35.805+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:01:35.806+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:01:35.806+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:01:35.835+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:01:35.835+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:01:35.835+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:01:35.836+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 信任管理问题","timestamp":"2025-06-25T14:01:35.836+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:01:35.836+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:01:35.836+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:01:35.837+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"信任管理问题\"}","timestamp":"2025-06-25T14:01:35.837+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:01:35.850+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:01:35.850+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:01:35.850+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:01:35.851+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 1","timestamp":"2025-06-25T14:01:35.851+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:01:35.851+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:01:35.852+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:01:35.852+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:01:35.868+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:01:35.868+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:01:35.869+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:01:35.869+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 权限许可和访问控制问题","timestamp":"2025-06-25T14:01:35.869+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:01:35.870+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:01:35.870+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:01:35.870+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"权限许可和访问控制问题\"}","timestamp":"2025-06-25T14:01:35.870+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:01:35.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:01:35.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:01:35.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:01:35.883+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 2","timestamp":"2025-06-25T14:01:35.884+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:01:35.884+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:01:35.884+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:01:35.885+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:01:35.895+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:01:35.896+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:01:35.896+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:01:35.897+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 处理逻辑错误","timestamp":"2025-06-25T14:01:35.897+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:01:35.897+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:01:35.897+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:01:35.898+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"处理逻辑错误\"}","timestamp":"2025-06-25T14:01:35.898+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:01:35.909+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:01:35.909+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:01:35.910+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:01:35.910+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 3","timestamp":"2025-06-25T14:01:35.910+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:01:35.910+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:01:35.911+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:01:35.911+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:01:35.923+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:01:35.923+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:01:35.923+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:01:35.924+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, API接口风险","timestamp":"2025-06-25T14:01:35.924+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:01:35.924+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:01:35.924+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:01:35.925+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"API接口风险\"}","timestamp":"2025-06-25T14:01:35.925+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:01:35.936+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:01:35.936+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:01:35.936+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:01:35.937+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 4","timestamp":"2025-06-25T14:01:35.937+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:01:35.937+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:213","level":"INFO","module":"agent","msg":"[node6] iter done","timestamp":"2025-06-25T14:01:35.938+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node7] node running, type: act_interpreter, node_name: 代码_YJ9nCb","timestamp":"2025-06-25T14:01:35.938+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node7] get node inputs","timestamp":"2025-06-25T14:01:35.938+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node7] get node inputs done","timestamp":"2025-06-25T14:01:35.938+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node7] code interpreter start","timestamp":"2025-06-25T14:01:44.109+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:100","level":"ERROR","module":"codeide","msg":"[node7] code interpreter error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-899dcbf2-1023-4391-adaa-39c9d911a50b/96163734_21ef_433e_9966_f537a6c67a02.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","timestamp":"2025-06-25T14:01:44.307+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node7] node run error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-899dcbf2-1023-4391-adaa-39c9d911a50b/96163734_21ef_433e_9966_f537a6c67a02.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","timestamp":"2025-06-25T14:01:44.308+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-899dcbf2-1023-4391-adaa-39c9d911a50b/96163734_21ef_433e_9966_f537a6c67a02.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","tid":"","timestamp":"2025-06-25T14:01:44.309+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:62638, status: 200","tid":"","timestamp":"2025-06-25T14:01:44.309+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T14:01:52.135+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T14:01:52.140+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T14:01:52.152+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T14:01:52.152+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T14:01:52.152+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T14:01:52.159+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T14:01:52.160+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T14:01:52.163+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T14:01:52.163+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T14:01:52.164+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T14:01:52.164+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node12] code interpreter start","timestamp":"2025-06-25T14:01:54.305+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node12] parse node output","timestamp":"2025-06-25T14:01:54.524+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node5] node running, type: llm_mixup, node_name: 大模型_fED3Ou","timestamp":"2025-06-25T14:01:54.525+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node5] get node inputs","timestamp":"2025-06-25T14:01:54.525+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node5] get node inputs done","timestamp":"2025-06-25T14:01:54.526+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:53","level":"INFO","module":"llm","msg":"[node5] chat llm request: {\"model\":\"HengNao-r1\",\"max_tokens\":4096,\"temperature\":0,\"top_k\":-1,\"top_p\":1,\"repetition_penalty\":0,\"reasoning\":true}","timestamp":"2025-06-25T14:01:54.533+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:68","level":"INFO","module":"llm","msg":"[node5] chat llm response: id:chatcmpl-58e459a67a8c4683bfb52e5a67e446bd, token_usage:3388 1669 6438, reason:stop","timestamp":"2025-06-25T14:03:19.862+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node5] parse node output","timestamp":"2025-06-25T14:03:19.863+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues_1","timestamp":"2025-06-25T14:03:19.863+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues","timestamp":"2025-06-25T14:03:19.864+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node6] node running, type: act_tool, node_name: 插件_2xnhgp","timestamp":"2025-06-25T14:03:19.864+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:03:19.865+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:03:19.865+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:03:19.866+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:03:19.887+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:03:19.887+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:03:19.888+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:03:19.888+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 信任管理问题","timestamp":"2025-06-25T14:03:19.888+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:03:19.889+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:03:19.889+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:03:19.889+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"信任管理问题\"}","timestamp":"2025-06-25T14:03:19.890+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:03:19.903+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:03:19.903+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:03:19.904+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:03:19.904+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 1","timestamp":"2025-06-25T14:03:19.905+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:03:19.905+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:03:19.906+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:03:19.906+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:03:19.917+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:03:19.918+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:03:19.918+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:03:19.919+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 权限许可和访问控制问题","timestamp":"2025-06-25T14:03:19.919+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:03:19.919+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:03:19.919+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:03:19.920+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"权限许可和访问控制问题\"}","timestamp":"2025-06-25T14:03:19.920+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:03:19.932+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:03:19.932+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:03:19.933+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:03:19.933+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 2","timestamp":"2025-06-25T14:03:19.933+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:03:19.934+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:03:19.935+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:03:19.935+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:03:19.946+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:03:19.946+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:03:19.946+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:03:19.946+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 处理逻辑错误","timestamp":"2025-06-25T14:03:19.947+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:03:19.947+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:03:19.947+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:03:19.947+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"处理逻辑错误\"}","timestamp":"2025-06-25T14:03:19.948+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:03:19.959+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:03:19.959+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:03:19.959+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:03:19.959+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 3","timestamp":"2025-06-25T14:03:19.960+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:03:19.960+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:03:19.960+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:03:19.961+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:03:19.974+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:03:19.974+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:03:19.974+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:03:19.974+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, API接口风险","timestamp":"2025-06-25T14:03:19.975+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:03:19.975+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:03:19.975+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:03:19.975+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"API接口风险\"}","timestamp":"2025-06-25T14:03:19.975+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:03:19.988+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:03:19.989+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:03:19.989+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:03:19.989+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 4","timestamp":"2025-06-25T14:03:19.989+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:03:19.990+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:213","level":"INFO","module":"agent","msg":"[node6] iter done","timestamp":"2025-06-25T14:03:19.990+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node7] node running, type: act_interpreter, node_name: 代码_YJ9nCb","timestamp":"2025-06-25T14:03:19.991+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node7] get node inputs","timestamp":"2025-06-25T14:03:19.991+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node7] get node inputs done","timestamp":"2025-06-25T14:03:19.991+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node7] code interpreter start","timestamp":"2025-06-25T14:03:24.445+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:100","level":"ERROR","module":"codeide","msg":"[node7] code interpreter error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-0ed186f9-e488-4714-8874-274492739291/b66e180b_1458_46c3_96de_5bc7f45cbe64.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","timestamp":"2025-06-25T14:03:24.650+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:49","level":"ERROR","module":"agent","msg":"[node7] node run error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-0ed186f9-e488-4714-8874-274492739291/b66e180b_1458_46c3_96de_5bc7f45cbe64.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","timestamp":"2025-06-25T14:03:24.651+0800"}
{"application":"secwalk","category":"interface","file":"handler/handler_code.go:138","level":"ERROR","module":"handler","msg":"restful response, status: 500, error: 代码执行错误 (Traceback (most recent call last):\n  File \"/var/sandbox/sandbox-python/tmp/sandbox-0ed186f9-e488-4714-8874-274492739291/b66e180b_1458_46c3_96de_5bc7f45cbe64.py\", line 62, in \u003cmodule\u003e\n    ret = mod.handler(runtime.Args(Params.inputs))\n  File \"\u003cstring\u003e\", line 13, in handler\nKeyError: 'mixRetrieveResults'\nerror: exit status 255\n)","tid":"","timestamp":"2025-06-25T14:03:24.652+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request final, client: 172.23.32.1:62638, status: 200","tid":"","timestamp":"2025-06-25T14:03:24.652+0800"}
{"application":"secwalk","category":"interface","file":"gin@v1.9.1/context.go:174","level":"INFO","module":"gin@v1.9.1","msg":"restful request entry, client: 172.23.32.1, method: POST, url: /secwalk/v1/agent/execute/debug, ext: ","tid":"","timestamp":"2025-06-25T14:03:58.925+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:55","level":"INFO","module":"agent","msg":"query session: ","timestamp":"2025-06-25T14:03:58.930+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:65","level":"INFO","module":"agent","msg":"selector gateway address","timestamp":"2025-06-25T14:03:58.944+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:77","level":"INFO","module":"agent","msg":"use core history","timestamp":"2025-06-25T14:03:58.945+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:93","level":"INFO","module":"agent","msg":"create agent instance","timestamp":"2025-06-25T14:03:58.945+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"service","file":"agent/agent_exec.go:167","level":"INFO","module":"agent","msg":"execute agent, inputs: {\"file\":\"file:CommandService.java\\u003c65335574-604e-4a31-8317-57db45acb254\\u003e\",\"input\":\"请分析\"}","timestamp":"2025-06-25T14:03:58.951+0800"}
{"application":"secwalk","category":"core","file":"endpoint/minio.go:58","level":"DEBUG","module":"endpoint","msg":"get file meta: file:CommandService.java\u003c65335574-604e-4a31-8317-57db45acb254\u003e","timestamp":"2025-06-25T14:03:58.951+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent.go:231","level":"INFO","module":"agent","msg":"[Lion测试] workflow start","timestamp":"2025-06-25T14:03:58.954+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node12] node running, type: act_interpreter, node_name: 代码_BjzwoN","timestamp":"2025-06-25T14:03:58.954+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node12] get node inputs","timestamp":"2025-06-25T14:03:58.954+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node12] get node inputs done","timestamp":"2025-06-25T14:03:58.955+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"codeide/codeide.go:66","level":"INFO","module":"codeide","msg":"[node12] code interpreter start","timestamp":"2025-06-25T14:04:02.392+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node12] parse node output","timestamp":"2025-06-25T14:04:02.606+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node5] node running, type: llm_mixup, node_name: 大模型_fED3Ou","timestamp":"2025-06-25T14:04:02.607+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node5] get node inputs","timestamp":"2025-06-25T14:04:02.607+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node5] get node inputs done","timestamp":"2025-06-25T14:04:02.608+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:53","level":"INFO","module":"llm","msg":"[node5] chat llm request: {\"model\":\"HengNao-r1\",\"max_tokens\":4096,\"temperature\":0,\"top_k\":-1,\"top_p\":1,\"repetition_penalty\":0,\"reasoning\":true}","timestamp":"2025-06-25T14:04:02.616+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://************:9994/api/licence/getKey\": dial tcp ************:9994: connect: connection refused","timestamp":"2025-06-25T14:05:07.421+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"llm/llm.go:68","level":"INFO","module":"llm","msg":"[node5] chat llm response: id:chatcmpl-862b7201a33e40d8b32220a825f07106, token_usage:3388 1669 6438, reason:stop","timestamp":"2025-06-25T14:05:30.106+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node5] parse node output","timestamp":"2025-06-25T14:05:30.106+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues_1","timestamp":"2025-06-25T14:05:30.107+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:535","level":"INFO","module":"agent","msg":"[node5] set parse kvs: issues","timestamp":"2025-06-25T14:05:30.107+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node6] node running, type: act_tool, node_name: 插件_2xnhgp","timestamp":"2025-06-25T14:05:30.108+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:05:30.108+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:05:30.108+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:05:30.109+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:05:30.135+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:05:30.135+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:05:30.135+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:05:30.136+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 信任管理问题","timestamp":"2025-06-25T14:05:30.136+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:05:30.136+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:05:30.136+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:05:30.136+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"信任管理问题\"}","timestamp":"2025-06-25T14:05:30.137+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:05:30.149+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:05:30.149+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:05:30.149+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:05:30.149+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 1","timestamp":"2025-06-25T14:05:30.149+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:05:30.150+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:05:30.150+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:05:30.150+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:05:30.163+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:05:30.163+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:05:30.163+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:05:30.163+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 权限许可和访问控制问题","timestamp":"2025-06-25T14:05:30.164+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:05:30.164+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:05:30.164+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:05:30.164+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"权限许可和访问控制问题\"}","timestamp":"2025-06-25T14:05:30.164+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:05:30.173+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:05:30.173+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:05:30.174+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:05:30.174+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 2","timestamp":"2025-06-25T14:05:30.174+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:05:30.174+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:05:30.175+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:05:30.175+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:05:30.184+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:05:30.184+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:05:30.184+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:05:30.184+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, 处理逻辑错误","timestamp":"2025-06-25T14:05:30.184+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:05:30.185+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:05:30.185+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:05:30.185+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"处理逻辑错误\"}","timestamp":"2025-06-25T14:05:30.185+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:05:30.195+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:05:30.195+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:05:30.196+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:05:30.196+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 3","timestamp":"2025-06-25T14:05:30.196+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:05:30.196+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node6] get node inputs done","timestamp":"2025-06-25T14:05:30.197+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/agent_act.go:59","level":"INFO","module":"agent","msg":"[node6] fetch tool: a11aead3-fa72-4983-9f64-d0be72ff92ee","timestamp":"2025-06-25T14:05:30.197+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:283","level":"INFO","module":"schema","msg":"[node6] use ability: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:05:30.204+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:84","level":"INFO","module":"restful","msg":"[node6] restful check ability params","timestamp":"2025-06-25T14:05:30.205+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: authorization, string, 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0","timestamp":"2025-06-25T14:05:30.205+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: Content-Type, string, application/json","timestamp":"2025-06-25T14:05:30.205+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: query, string, API接口风险","timestamp":"2025-06-25T14:05:30.205+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: orgId, string, 1937468441246044162","timestamp":"2025-06-25T14:05:30.205+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:96","level":"DEBUG","module":"restful","msg":"[node6] ability params: labels, array, [\"4236581100008823\"]","timestamp":"2025-06-25T14:05:30.205+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:188","level":"INFO","module":"restful","msg":"[node6] restful json request: http://************:18092","timestamp":"2025-06-25T14:05:30.206+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"restful/restful.go:205","level":"DEBUG","module":"restful","msg":"[node6] restful json request body: {\"labels\":[\"4236581100008823\"],\"orgId\":\"1937468441246044162\",\"query\":\"API接口风险\"}","timestamp":"2025-06-25T14:05:30.206+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: mixRetrieveResults","timestamp":"2025-06-25T14:05:30.215+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:432","level":"WARN","module":"schema","msg":"[node6] miss found tool param: cost","timestamp":"2025-06-25T14:05:30.215+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"schema/toolkit.go:439","level":"INFO","module":"schema","msg":"[node6] use ability success: a11aead3-fa72-4983-9f64-d0be72ff92ee 原文内容召回","timestamp":"2025-06-25T14:05:30.216+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:481","level":"INFO","module":"agent","msg":"[node6] parse node output","timestamp":"2025-06-25T14:05:30.216+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:342","level":"INFO","module":"agent","msg":"[node6] iterate node times: 4","timestamp":"2025-06-25T14:05:30.216+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node6] get node inputs","timestamp":"2025-06-25T14:05:30.216+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:213","level":"INFO","module":"agent","msg":"[node6] iter done","timestamp":"2025-06-25T14:05:30.217+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:143","level":"INFO","module":"agent","msg":"[node7] node running, type: act_interpreter, node_name: 代码_YJ9nCb","timestamp":"2025-06-25T14:05:30.217+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:357","level":"INFO","module":"agent","msg":"[node7] get node inputs","timestamp":"2025-06-25T14:05:30.217+0800"}
{"aid":"7b64df53-e9d3-43fa-a1de-b3819f62c2ab","application":"secwalk","category":"core","file":"agent/workflow_exec.go:469","level":"INFO","module":"agent","msg":"[node7] get node inputs done","timestamp":"2025-06-25T14:05:30.217+0800"}
{"application":"secwalk","category":"system","file":"cmd/main.go:12","level":"ERROR","module":"cmd","msg":"secwalk run error: mysql: querying mysql version dial tcp ************:3306: connect: connection refused","timestamp":"2025-06-26T10:46:13.588+0800"}
{"application":"secwalk","category":"system","file":"cmd/main.go:12","level":"ERROR","module":"cmd","msg":"secwalk run error: mysql: querying mysql version dial tcp ************:3306: connect: connection refused","timestamp":"2025-06-26T10:47:49.020+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: 10.20.152.120:9000","timestamp":"2025-06-27T17:47:49.158+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-27T17:47:49.173+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct 10.20.152.120:9994","timestamp":"2025-06-27T17:47:49.175+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct 10.20.152.120:8994","timestamp":"2025-06-27T17:47:49.275+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct 10.20.152.120:8995","timestamp":"2025-06-27T17:47:49.375+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct 10.20.152.120:18092","timestamp":"2025-06-27T17:47:49.477+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct 10.20.152.120:9996","timestamp":"2025-06-27T17:47:49.578+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-27T17:47:49.679+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-27T17:47:49.680+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-27T17:47:49.682+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-27T17:47:49.684+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://10.20.152.120:9994/api/licence/getKey\": dial tcp 10.20.152.120:9994: connect: connection refused","timestamp":"2025-06-27T17:47:49.690+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-27T17:47:49.973+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-27T17:47:49.973+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-27T17:47:49.974+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-27T17:47:49.974+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-27T17:47:49.974+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-27T17:47:49.974+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-27T17:47:49.975+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-27T17:47:49.975+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-27T17:47:49.975+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-27T17:47:49.975+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-27T17:47:49.975+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-27T17:47:49.975+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-27T17:47:49.976+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-27T17:47:49.976+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-27T17:47:49.976+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-27T17:47:49.976+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-27T17:47:49.977+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-27T17:47:49.977+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-27T17:47:49.977+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-27T17:47:49.977+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-27T17:47:49.978+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-27T17:47:49.978+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-27T17:47:49.978+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-27T17:47:49.979+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-27T17:47:49.979+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-27T17:47:49.979+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-27T17:47:49.979+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-27T17:47:49.980+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-27T17:47:49.980+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-27T17:47:49.980+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-27T17:47:49.980+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-27T17:47:49.981+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-27T17:47:49.981+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-27T17:47:49.981+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-27T17:47:49.981+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-27T17:47:49.982+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-27T17:47:49.983+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-27T17:47:49.986+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-36a677c0-cbfb-477f-a2c7-eb1d924a021b","timestamp":"2025-06-27T17:47:49.986+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: 10.20.152.120:9000","timestamp":"2025-06-27T17:51:08.338+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-27T17:51:08.345+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct 10.20.152.120:9994","timestamp":"2025-06-27T17:51:08.346+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct 10.20.152.120:8994","timestamp":"2025-06-27T17:51:08.447+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct 10.20.152.120:8995","timestamp":"2025-06-27T17:51:08.548+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct 10.20.152.120:18092","timestamp":"2025-06-27T17:51:08.648+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct 10.20.152.120:9996","timestamp":"2025-06-27T17:51:08.748+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-27T17:51:08.849+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-27T17:51:08.850+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-27T17:51:08.851+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-27T17:51:08.853+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://10.20.152.120:9994/api/licence/getKey\": dial tcp 10.20.152.120:9994: connect: connection refused","timestamp":"2025-06-27T17:51:08.859+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-27T17:51:09.057+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-27T17:51:09.058+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-27T17:51:09.058+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-27T17:51:09.058+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-27T17:51:09.059+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-27T17:51:09.059+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-27T17:51:09.059+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-27T17:51:09.060+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-27T17:51:09.060+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-27T17:51:09.060+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-27T17:51:09.060+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-27T17:51:09.061+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-27T17:51:09.061+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-27T17:51:09.061+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-27T17:51:09.061+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-27T17:51:09.062+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-27T17:51:09.062+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-27T17:51:09.062+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-27T17:51:09.062+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-27T17:51:09.063+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-27T17:51:09.063+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-27T17:51:09.063+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-27T17:51:09.063+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-27T17:51:09.064+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-27T17:51:09.064+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-27T17:51:09.064+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-27T17:51:09.064+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-27T17:51:09.065+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-27T17:51:09.065+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-27T17:51:09.065+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-27T17:51:09.065+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-27T17:51:09.066+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-27T17:51:09.066+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-27T17:51:09.066+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-27T17:51:09.066+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-27T17:51:09.066+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-27T17:51:09.067+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-27T17:51:09.072+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-fe804f20-34a0-49e3-b043-4a4a6e290cb5","timestamp":"2025-06-27T17:51:09.073+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://10.20.152.120:9994/api/licence/getKey\": dial tcp 10.20.152.120:9994: connect: connection refused","timestamp":"2025-06-27T17:56:08.864+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://10.20.152.120:9994/api/licence/getKey\": dial tcp 10.20.152.120:9994: connect: connection refused","timestamp":"2025-06-27T18:01:08.870+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:79","level":"INFO","module":"app","msg":"init minio client: 10.20.152.120:9000","timestamp":"2025-06-27T18:03:31.900+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:86","level":"INFO","module":"app","msg":"init server selector","timestamp":"2025-06-27T18:03:31.903+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: openapi direct 10.20.152.120:9996","timestamp":"2025-06-27T18:03:31.903+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: backend direct 10.20.152.120:9994","timestamp":"2025-06-27T18:03:32.003+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secvirt direct 10.20.152.120:8994","timestamp":"2025-06-27T18:03:32.103+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: secwall direct 10.20.152.120:8995","timestamp":"2025-06-27T18:03:32.204+0800"}
{"application":"secwalk","category":"selector","file":"selector/selector.go:231","level":"DEBUG","module":"selector","msg":"online instance: hb-rag direct 10.20.152.120:18092","timestamp":"2025-06-27T18:03:32.305+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:93","level":"INFO","module":"app","msg":"init license","timestamp":"2025-06-27T18:03:32.406+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:48","level":"WARN","module":"license","msg":"env license fetch error: unexpected end of JSON input","timestamp":"2025-06-27T18:03:32.407+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:98","level":"INFO","module":"app","msg":"init storage repo","timestamp":"2025-06-27T18:03:32.409+0800"}
{"application":"secwalk","category":"system","file":"xguard/xguard.go:44","level":"INFO","module":"xguard","msg":"init trait xguard","timestamp":"2025-06-27T18:03:32.413+0800"}
{"application":"secwalk","category":"system","file":"license/license.go:55","level":"WARN","module":"license","msg":"license fetch error: Get \"http://10.20.152.120:9994/api/licence/getKey\": dial tcp 10.20.152.120:9994: connect: connection refused","timestamp":"2025-06-27T18:03:32.420+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent              --- secwalk/internal/server/handler.(*AgentHandler).agent-fm","timestamp":"2025-06-27T18:03:32.610+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute      --- secwalk/internal/server/handler.(*AgentHandler).agentExecute-fm","timestamp":"2025-06-27T18:03:32.610+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/debug --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteDebug-fm","timestamp":"2025-06-27T18:03:32.611+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/execute/resume --- secwalk/internal/server/handler.(*AgentHandler).agentExecuteResume-fm","timestamp":"2025-06-27T18:03:32.611+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/export       --- secwalk/internal/server/handler.(*AgentHandler).agentExport-fm","timestamp":"2025-06-27T18:03:32.611+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update       --- secwalk/internal/server/handler.(*AgentHandler).agentUpdate-fm","timestamp":"2025-06-27T18:03:32.611+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/update/extension --- secwalk/internal/server/handler.(*AgentHandler).agentUpdateExtension-fm","timestamp":"2025-06-27T18:03:32.611+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/upsert       --- secwalk/internal/server/handler.(*AgentHandler).agentUpsert-fm","timestamp":"2025-06-27T18:03:32.612+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/submit  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskSubmit-fm","timestamp":"2025-06-27T18:03:32.612+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/cancel  --- secwalk/internal/server/handler.(*AgentHandler).agentTaskCancel-fm","timestamp":"2025-06-27T18:03:32.612+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/task/messages --- secwalk/internal/server/handler.(*AgentHandler).agentTaskMessages-fm","timestamp":"2025-06-27T18:03:32.612+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/import       --- secwalk/internal/server/handler.(*AgentHandler).agentImport-fm","timestamp":"2025-06-27T18:03:32.612+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/create       --- secwalk/internal/server/handler.(*AgentHandler).agentCreate-fm","timestamp":"2025-06-27T18:03:32.613+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/delete/:id   --- secwalk/internal/server/handler.(*AgentHandler).agentDelete-fm","timestamp":"2025-06-27T18:03:32.613+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/agent/verify       --- secwalk/internal/server/handler.(*AgentHandler).agentVerify-fm","timestamp":"2025-06-27T18:03:32.613+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute    --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecute-fm","timestamp":"2025-06-27T18:03:32.614+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/ability/execute/debug --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitExecuteDebug-fm","timestamp":"2025-06-27T18:03:32.614+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit            --- secwalk/internal/server/handler.(*ToolkitHandler).toolkit-fm","timestamp":"2025-06-27T18:03:32.614+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/update     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpdate-fm","timestamp":"2025-06-27T18:03:32.614+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/upsert     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitUpsert-fm","timestamp":"2025-06-27T18:03:32.614+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/system     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitSystem-fm","timestamp":"2025-06-27T18:03:32.615+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/create     --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitCreate-fm","timestamp":"2025-06-27T18:03:32.615+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/toolkit/delete/:id --- secwalk/internal/server/handler.(*ToolkitHandler).toolkitDelete-fm","timestamp":"2025-06-27T18:03:32.615+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/upsert --- secwalk/internal/server/handler.(*XGuardHandler).traitUpsert-fm","timestamp":"2025-06-27T18:03:32.615+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/insert --- secwalk/internal/server/handler.(*XGuardHandler).traitInsert-fm","timestamp":"2025-06-27T18:03:32.616+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/xguard/trait/delete --- secwalk/internal/server/handler.(*XGuardHandler).traitDelete-fm","timestamp":"2025-06-27T18:03:32.616+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/server/info    --- secwalk/internal/server/handler.(*MCPHandler).mcpServerInfo-fm","timestamp":"2025-06-27T18:03:32.616+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/upsert         --- secwalk/internal/server/handler.(*MCPHandler).mcpUpsert-fm","timestamp":"2025-06-27T18:03:32.616+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/mcp/delete/:id     --- secwalk/internal/server/handler.(*MCPHandler).mcpDelete-fm","timestamp":"2025-06-27T18:03:32.617+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/session/insert     --- secwalk/internal/server/handler.(*SessionHandler).sessionInsert-fm","timestamp":"2025-06-27T18:03:32.617+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /secwalk/v1/license/refresh    --- secwalk/internal/server/handler.(*SessionHandler).licenseRefresh-fm","timestamp":"2025-06-27T18:03:32.617+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: POST  /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).postOssFile-fm","timestamp":"2025-06-27T18:03:32.617+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/agent/ids          --- secwalk/internal/server/handler.(*AgentHandler).agentIDs-fm","timestamp":"2025-06-27T18:03:32.617+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/session/:id        --- secwalk/internal/server/handler.(*SessionHandler).session-fm","timestamp":"2025-06-27T18:03:32.618+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /secwalk/v1/xguard/trait       --- secwalk/internal/server/handler.(*XGuardHandler).trait-fm","timestamp":"2025-06-27T18:03:32.618+0800"}
{"application":"secwalk","category":"system","file":"handler/handler.go:52","level":"INFO","module":"handler","msg":"register route: GET   /api/v1/oss/file/:id           --- secwalk/internal/server/handler.(*APIHandler).getOssFile-fm","timestamp":"2025-06-27T18:03:32.618+0800"}
{"application":"secwalk","category":"system","file":"app/app.go:143","level":"INFO","module":"app","msg":"Starting [service] secwalk","timestamp":"2025-06-27T18:03:32.619+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:281","level":"INFO","module":"http@v1.2.2","msg":"Listening on [::]:8998","timestamp":"2025-06-27T18:03:32.624+0800"}
{"application":"secwalk","category":"system","file":"http@v1.2.2/http.go:181","level":"INFO","module":"http@v1.2.2","msg":"Registering node: secwalk-46faf778-4d5c-4146-bc2d-da2d4f35d24f","timestamp":"2025-06-27T18:03:32.625+0800"}
