# SecWalk AWS OTEL Collector 集成指南

本文档介绍如何将 SecWalk 服务与 AWS OTEL Collector 集成，实现分布式追踪、指标收集和日志聚合。

## 架构概述

```
┌─────────────────┐    ┌──────────────────────┐    ┌─────────────────┐
│   SecWalk       │───▶│  AWS OTEL Collector  │───▶│   AWS Services  │
│   Service       │    │                      │    │                 │
│                 │    │  - Traces → X-Ray    │    │  - X-Ray        │
│  - HTTP API     │    │  - Metrics → CW      │    │  - CloudWatch   │
│  - OpenTelemetry│    │  - Logs → CW Logs    │    │  - CW Logs      │
└─────────────────┘    └──────────────────────┘    └─────────────────┘
```

## 功能特性

### 1. 分布式追踪 (Distributed Tracing)
- 自动追踪 HTTP 请求
- 跨服务调用链路追踪
- 错误和异常记录
- 性能瓶颈识别

### 2. 指标收集 (Metrics Collection)
- HTTP 请求计数和延迟
- 系统资源使用情况
- 自定义业务指标
- 实时性能监控

### 3. 日志聚合 (Log Aggregation)
- 结构化日志收集
- 分布式日志关联
- 错误日志告警

## 快速开始

### 1. 环境准备

设置 AWS 凭证：
```bash
export AWS_REGION=us-east-1
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
```

### 2. 安装依赖

```bash
# 安装 Go 依赖
go mod tidy

# 确保 Docker 和 Docker Compose 已安装
docker --version
docker-compose --version
```

### 3. 配置文件

#### 开发环境 (configs/dev.yaml)
```yaml
telemetry:
  enabled: true
  service_name: secwalk
  service_version: v1.0.0
  environment: development
  otlp_endpoint: http://localhost:4318
  sample_rate: 1.0  # 100% 采样
```

#### 生产环境 (configs/pro.yaml)
```yaml
telemetry:
  enabled: true
  service_name: secwalk
  service_version: v1.0.0
  environment: production
  otlp_endpoint: http://aws-otel-collector:4318
  sample_rate: 0.1  # 10% 采样
```

### 4. 部署服务

使用提供的部署脚本：
```bash
# 给脚本执行权限
chmod +x deploy-with-otel.sh

# 启动服务
./deploy-with-otel.sh start

# 查看服务状态
./deploy-with-otel.sh status

# 查看日志
./deploy-with-otel.sh logs

# 停止服务
./deploy-with-otel.sh stop
```

或者手动使用 Docker Compose：
```bash
# 启动服务
docker-compose -f docker-compose.otel.yaml up -d

# 查看日志
docker-compose -f docker-compose.otel.yaml logs -f

# 停止服务
docker-compose -f docker-compose.otel.yaml down
```

## 服务端点

### SecWalk 服务
- **API 端点**: http://localhost:8998
- **健康检查**: http://localhost:8998/secwalk/v1/health
- **就绪检查**: http://localhost:8998/secwalk/v1/ready

### AWS OTEL Collector
- **OTLP gRPC**: localhost:4317
- **OTLP HTTP**: localhost:4318
- **指标端点**: http://localhost:8888/metrics
- **健康检查**: http://localhost:13133/

## 监控和观测

### 1. AWS X-Ray 追踪
在 AWS 控制台中查看：
1. 打开 AWS X-Ray 服务
2. 查看服务地图 (Service Map)
3. 分析追踪详情 (Traces)

### 2. CloudWatch 指标
在 AWS CloudWatch 中查看：
1. 打开 CloudWatch 控制台
2. 导航到 "指标" → "SecWalk/Application"
3. 查看自定义指标

### 3. CloudWatch 日志
在 AWS CloudWatch Logs 中查看：
1. 打开 CloudWatch Logs 控制台
2. 查找日志组 "/aws/secwalk/application"

## 配置说明

### OpenTelemetry 配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `enabled` | 是否启用遥测 | `false` |
| `service_name` | 服务名称 | `secwalk` |
| `service_version` | 服务版本 | `v1.0.0` |
| `environment` | 部署环境 | `production` |
| `otlp_endpoint` | OTLP 端点地址 | `http://localhost:4318` |
| `sample_rate` | 追踪采样率 | `1.0` |

### OTEL Collector 配置

主要配置文件：`otel-collector-config.yaml`

#### 接收器 (Receivers)
- **OTLP**: 接收来自应用的遥测数据
- **Prometheus**: 抓取 Prometheus 格式的指标
- **Host Metrics**: 收集主机系统指标

#### 处理器 (Processors)
- **Batch**: 批量处理提高性能
- **Resource**: 添加资源属性
- **Memory Limiter**: 内存限制防止 OOM

#### 导出器 (Exporters)
- **AWS X-Ray**: 导出追踪数据到 X-Ray
- **CloudWatch Metrics**: 导出指标到 CloudWatch
- **CloudWatch Logs**: 导出日志到 CloudWatch Logs

## 故障排除

### 1. 服务启动失败
```bash
# 检查容器状态
docker ps -a

# 查看容器日志
docker logs hn-secwalk
docker logs aws-otel-collector
```

### 2. 遥测数据未发送
1. 检查配置文件中 `telemetry.enabled` 是否为 `true`
2. 验证 OTLP 端点地址是否正确
3. 检查网络连接

### 3. AWS 权限问题
确保 AWS 凭证具有以下权限：
- `xray:PutTraceSegments`
- `xray:PutTelemetryRecords`
- `cloudwatch:PutMetricData`
- `logs:CreateLogGroup`
- `logs:CreateLogStream`
- `logs:PutLogEvents`

### 4. 性能问题
1. 调整采样率 (`sample_rate`)
2. 增加批处理大小
3. 调整内存限制

## 最佳实践

### 1. 采样策略
- **开发环境**: 100% 采样 (`sample_rate: 1.0`)
- **测试环境**: 50% 采样 (`sample_rate: 0.5`)
- **生产环境**: 5-10% 采样 (`sample_rate: 0.05-0.1`)

### 2. 资源标签
确保为所有资源添加适当的标签：
- `service.name`
- `service.version`
- `deployment.environment`
- `service.instance.id`

### 3. 告警配置
在 CloudWatch 中设置关键指标的告警：
- HTTP 错误率
- 响应时间
- 服务可用性

### 4. 成本优化
- 合理设置采样率
- 定期清理旧的日志和追踪数据
- 使用 CloudWatch 日志保留策略

## 扩展功能

### 1. 自定义指标
在代码中添加自定义指标：
```go
import "secwalk/pkg/telemetry"

// 获取 meter
meter := telemetry.GetMeter()

// 创建计数器
counter, _ := meter.Int64Counter("custom_operations_total")

// 记录指标
counter.Add(ctx, 1, metric.WithAttributes(
    attribute.String("operation", "process_document"),
))
```

### 2. 自定义追踪
添加自定义 span：
```go
import "secwalk/pkg/telemetry"

// 开始 span
ctx, span := telemetry.StartSpan(ctx, "custom_operation")
defer span.End()

// 添加属性
span.SetAttributes(
    attribute.String("user_id", userID),
    attribute.Int("document_size", size),
)

// 记录错误
if err != nil {
    telemetry.RecordError(ctx, err)
}
```

## 参考资料

- [AWS Distro for OpenTelemetry](https://aws-otel.github.io/docs/)
- [OpenTelemetry Go SDK](https://opentelemetry.io/docs/instrumentation/go/)
- [AWS X-Ray Developer Guide](https://docs.aws.amazon.com/xray/)
- [Amazon CloudWatch User Guide](https://docs.aws.amazon.com/cloudwatch/)
