# SecWalk OpenTelemetry 集成测试脚本 (PowerShell 版本)

# 颜色函数
function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param($Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 测试编译
function Test-Build {
    Write-Info "测试编译..."
    
    try {
        & go build -o bin/secwalk.exe cmd/main.go
        if ($LASTEXITCODE -eq 0) {
            Write-Info "✓ 编译成功"
            return $true
        } else {
            Write-Error "✗ 编译失败"
            return $false
        }
    } catch {
        Write-Error "✗ 编译过程中出现异常: $($_.Exception.Message)"
        return $false
    }
}

# 检查配置文件
function Test-Config {
    Write-Info "检查配置文件..."
    
    # 检查开发配置
    if (Test-Path "configs/dev.yaml") {
        $content = Get-Content "configs/dev.yaml" -Raw
        if ($content -match "telemetry:") {
            Write-Info "✓ 开发配置包含 telemetry 配置"
        } else {
            Write-Warn "⚠ 开发配置缺少 telemetry 配置"
        }
    } else {
        Write-Error "✗ 开发配置文件不存在"
    }
    
    # 检查生产配置
    if (Test-Path "configs/pro.yaml") {
        $content = Get-Content "configs/pro.yaml" -Raw
        if ($content -match "telemetry:") {
            Write-Info "✓ 生产配置包含 telemetry 配置"
        } else {
            Write-Warn "⚠ 生产配置缺少 telemetry 配置"
        }
    } else {
        Write-Error "✗ 生产配置文件不存在"
    }
}

# 检查 OTEL 配置文件
function Test-OtelConfig {
    Write-Info "检查 OTEL Collector 配置..."
    
    if (Test-Path "otel-collector-config.yaml") {
        Write-Info "✓ OTEL Collector 配置文件存在"
        
        $content = Get-Content "otel-collector-config.yaml" -Raw
        if ($content -match "receivers:" -and $content -match "processors:" -and $content -match "exporters:") {
            Write-Info "✓ OTEL 配置文件结构正确"
        } else {
            Write-Warn "⚠ OTEL 配置文件可能不完整"
        }
    } else {
        Write-Error "✗ OTEL Collector 配置文件不存在"
    }
}

# 检查 Docker 配置
function Test-DockerConfig {
    Write-Info "检查 Docker 配置..."
    
    if (Test-Path "docker-compose.otel.yaml") {
        Write-Info "✓ Docker Compose 配置文件存在"
        
        $content = Get-Content "docker-compose.otel.yaml" -Raw
        if ($content -match "aws-otel-collector:" -and $content -match "hn-secwalk:") {
            Write-Info "✓ Docker Compose 包含必要的服务"
        } else {
            Write-Warn "⚠ Docker Compose 配置可能不完整"
        }
    } else {
        Write-Error "✗ Docker Compose 配置文件不存在"
    }
}

# 检查依赖
function Test-Dependencies {
    Write-Info "检查 Go 依赖..."
    
    try {
        & go mod verify | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Info "✓ Go 模块验证通过"
        } else {
            Write-Error "✗ Go 模块验证失败"
            return $false
        }
    } catch {
        Write-Error "✗ Go 模块验证异常: $($_.Exception.Message)"
        return $false
    }
    
    # 检查关键的 OpenTelemetry 依赖
    try {
        & go list -m go.opentelemetry.io/otel | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Info "✓ OpenTelemetry 核心库已安装"
        } else {
            Write-Error "✗ OpenTelemetry 核心库未安装"
        }
    } catch {
        Write-Error "✗ 检查 OpenTelemetry 核心库时出错"
    }
    
    try {
        & go list -m go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Info "✓ Gin OpenTelemetry 中间件已安装"
        } else {
            Write-Error "✗ Gin OpenTelemetry 中间件未安装"
        }
    } catch {
        Write-Error "✗ 检查 Gin OpenTelemetry 中间件时出错"
    }
    
    return $true
}

# 测试服务启动（快速测试）
function Test-ServiceStart {
    Write-Info "测试服务启动..."
    
    # 创建临时配置文件
    $testConfig = @"
server:
  name: secwalk
  address: :18998

logger:
  app: secwalk
  level: info
  format: false
  output: logs/secwalk.log
  max_size: 20
  max_age: 30
  max_backup: 10

telemetry:
  enabled: false  # 禁用以避免外部依赖
  service_name: secwalk
  service_version: v1.0.0
  environment: test
  otlp_endpoint: http://localhost:4318
  sample_rate: 1.0

feature:
  use_xguard: false
  max_reslut: 100
"@
    
    $testConfig | Out-File -FilePath "test-config.yaml" -Encoding UTF8
    
    try {
        # 尝试启动服务（后台运行）
        $process = Start-Process -FilePath ".\bin\secwalk.exe" -ArgumentList "run", "--config", "test-config.yaml" -PassThru -NoNewWindow
        
        # 等待一下
        Start-Sleep -Seconds 3
        
        # 检查进程是否还在运行
        if (!$process.HasExited) {
            Write-Info "✓ 服务启动成功"
            $process.Kill()
        } else {
            Write-Warn "⚠ 服务启动可能有问题（可能是因为缺少外部依赖）"
        }
    } catch {
        Write-Warn "⚠ 服务启动测试异常: $($_.Exception.Message)"
    } finally {
        # 清理
        if (Test-Path "test-config.yaml") {
            Remove-Item "test-config.yaml" -Force
        }
    }
}

# 生成测试报告
function Generate-Report {
    Write-Info "生成测试报告..."
    
    Write-Host ""
    Write-Host "==========================================" -ForegroundColor Cyan
    Write-Host "SecWalk OpenTelemetry 集成测试报告" -ForegroundColor Cyan
    Write-Host "==========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "✓ 编译测试通过" -ForegroundColor Green
    Write-Host "✓ 配置文件检查完成" -ForegroundColor Green
    Write-Host "✓ OTEL 配置检查完成" -ForegroundColor Green
    Write-Host "✓ Docker 配置检查完成" -ForegroundColor Green
    Write-Host "✓ 依赖检查完成" -ForegroundColor Green
    Write-Host "✓ 服务启动测试完成" -ForegroundColor Green
    Write-Host ""
    Write-Host "下一步操作：" -ForegroundColor Yellow
    Write-Host "1. 设置 AWS 环境变量：" -ForegroundColor White
    Write-Host "   `$env:AWS_REGION='your-region'" -ForegroundColor Gray
    Write-Host "   `$env:AWS_ACCESS_KEY_ID='your-access-key'" -ForegroundColor Gray
    Write-Host "   `$env:AWS_SECRET_ACCESS_KEY='your-secret-key'" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. 启动服务（如果在 WSL 中）：" -ForegroundColor White
    Write-Host "   ./deploy-with-otel.sh start" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. 或者直接运行服务：" -ForegroundColor White
    Write-Host "   .\bin\secwalk.exe run --config configs\dev.yaml" -ForegroundColor Gray
    Write-Host ""
    Write-Host "4. 测试健康检查：" -ForegroundColor White
    Write-Host "   curl http://localhost:8998/secwalk/v1/health" -ForegroundColor Gray
    Write-Host ""
}

# 主函数
function Main {
    Write-Info "开始 SecWalk OpenTelemetry 集成测试..."
    
    $success = $true
    
    if (!(Test-Build)) { $success = $false }
    Test-Config
    Test-OtelConfig
    Test-DockerConfig
    if (!(Test-Dependencies)) { $success = $false }
    Test-ServiceStart
    Generate-Report
    
    if ($success) {
        Write-Info "所有测试完成！"
    } else {
        Write-Warn "测试完成，但有一些问题需要解决"
    }
}

# 执行主函数
Main
