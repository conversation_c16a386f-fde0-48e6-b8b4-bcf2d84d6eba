#!/bin/bash

# SecWalk with AWS OTEL Collector 部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的环境变量
check_env() {
    log_info "检查环境变量..."
    
    if [ -z "$AWS_REGION" ]; then
        log_error "AWS_REGION 环境变量未设置"
        exit 1
    fi
    
    if [ -z "$AWS_ACCESS_KEY_ID" ]; then
        log_error "AWS_ACCESS_KEY_ID 环境变量未设置"
        exit 1
    fi
    
    if [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
        log_error "AWS_SECRET_ACCESS_KEY 环境变量未设置"
        exit 1
    fi
    
    log_info "环境变量检查完成"
}

# 检查 Docker 网络
check_network() {
    log_info "检查 Docker 网络..."
    
    if ! docker network ls | grep -q "hn-network"; then
        log_warn "hn-network 不存在，正在创建..."
        docker network create hn-network
        log_info "hn-network 创建完成"
    else
        log_info "hn-network 已存在"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    chmod 0777 logs
    
    log_info "目录创建完成"
}

# 构建服务镜像
build_service() {
    log_info "构建 SecWalk 服务镜像..."
    
    if [ -f "Dockerfile" ]; then
        docker build -t docker.das-security.cn/secark/secwalk:latest .
        log_info "SecWalk 镜像构建完成"
    else
        log_warn "Dockerfile 不存在，跳过镜像构建"
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 停止现有服务
    docker-compose -f docker-compose.otel.yaml down 2>/dev/null || true
    
    # 启动新服务
    docker-compose -f docker-compose.otel.yaml up -d
    
    log_info "服务启动完成"
}

# 检查服务健康状态
check_health() {
    log_info "检查服务健康状态..."
    
    # 等待服务启动
    sleep 10
    
    # 检查 OTEL Collector
    if docker ps | grep -q "aws-otel-collector"; then
        log_info "✓ AWS OTEL Collector 运行正常"
    else
        log_error "✗ AWS OTEL Collector 启动失败"
        return 1
    fi
    
    # 检查 SecWalk 服务
    if docker ps | grep -q "hn-secwalk"; then
        log_info "✓ SecWalk 服务运行正常"
    else
        log_error "✗ SecWalk 服务启动失败"
        return 1
    fi
    
    # 检查端口
    if curl -f http://localhost:8998/secwalk/v1/health >/dev/null 2>&1; then
        log_info "✓ SecWalk 健康检查通过"
    else
        log_warn "⚠ SecWalk 健康检查失败，可能还在启动中"
    fi
    
    # 检查 OTEL Collector 指标端点
    if curl -f http://localhost:8888/metrics >/dev/null 2>&1; then
        log_info "✓ OTEL Collector 指标端点正常"
    else
        log_warn "⚠ OTEL Collector 指标端点不可访问"
    fi
}

# 显示服务信息
show_info() {
    log_info "服务部署完成！"
    echo ""
    echo "服务访问信息："
    echo "  SecWalk API: http://localhost:8998"
    echo "  OTEL Collector 指标: http://localhost:8888/metrics"
    echo "  OTEL Collector 健康检查: http://localhost:13133/"
    echo ""
    echo "查看日志："
    echo "  docker-compose -f docker-compose.otel.yaml logs -f"
    echo ""
    echo "停止服务："
    echo "  docker-compose -f docker-compose.otel.yaml down"
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            log_info "开始部署 SecWalk with AWS OTEL Collector..."
            check_env
            check_network
            create_directories
            build_service
            start_services
            check_health
            show_info
            ;;
        "stop")
            log_info "停止服务..."
            docker-compose -f docker-compose.otel.yaml down
            log_info "服务已停止"
            ;;
        "restart")
            log_info "重启服务..."
            docker-compose -f docker-compose.otel.yaml restart
            check_health
            log_info "服务重启完成"
            ;;
        "logs")
            docker-compose -f docker-compose.otel.yaml logs -f
            ;;
        "status")
            docker-compose -f docker-compose.otel.yaml ps
            ;;
        *)
            echo "用法: $0 {start|stop|restart|logs|status}"
            echo ""
            echo "命令说明："
            echo "  start   - 启动服务 (默认)"
            echo "  stop    - 停止服务"
            echo "  restart - 重启服务"
            echo "  logs    - 查看日志"
            echo "  status  - 查看服务状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
