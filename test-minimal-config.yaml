# 最小化测试配置
server:
  name: secwalk
  address: :18998

logger:
  app: secwalk
  level: info
  format: false
  output: logs/secwalk.log
  max_size: 20
  max_age: 30
  max_backup: 10

# 禁用服务注册以避免外部依赖
register:
  type: ""
  host: ""
  token: ""

# 禁用外部服务以避免依赖
services: {}

# 禁用数据库以避免依赖
db:
  address: ""
  username: ""
  password: ""

# 禁用 MinIO 以避免依赖
minio:
  host: ""
  access_key: ""
  secret_key: ""

# 禁用功能以避免依赖
feature:
  use_xguard: false
  max_reslut: 100

# OpenTelemetry 配置（禁用以避免外部依赖）
telemetry:
  enabled: false
  service_name: secwalk
  service_version: v1.0.0
  environment: test
  otlp_endpoint: http://localhost:4318
  sample_rate: 1.0
