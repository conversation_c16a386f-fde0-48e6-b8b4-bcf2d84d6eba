# 服务基本配置
server:
  name: secwalk
  address: :8998

# 日志配置
logger:
  app: secwalk
  level: debug
  format: false
  output: logs/secwalk.log
  max_size: 20
  max_age: 30
  max_backup: 10

# 服务注册配置
register:
  type: consul
  host: ***********:8500
  token: d9295a36-9f55-4580-b22d-71cfa5837f76
  username:
  password:


# 服务发现配置
services:
  backend:
    type: direct
    name: backend
    host: *************:9994
    metas:
      authorization: 9KsxdRjPXY95CP4WHSytwXW7QYhBrtEV
  openapi:
    type: direct
    name: openapi
    host: *************:9996
    metas:
      authorization: 9KsxdRjPXY95CP4WHSytwXW7QYhBrtEV
  gateway:
    type: direct
    name: secwall
    host: *************:8995
    metas:
      authorization: "7178648308129206272"
  coder:
    type: direct
    name: secvirt
    host: *************:8994
  hbrag:
    type: direct
    name: hb-rag
    host: *************:18092
    metas:
      authorization: 5M1048xJby2q2s6H4Sn9Hv5guYu2tUW0

# 存储配置
db:
  address: *************:3306
  username: root
  password: J7aXgk2BJUj=

minio:
  host: *************:9000
  access_key: admin
  secret_key: tYqV9s#bt3ADrMy

# 内部功能开关
feature:
  use_xguard: true
  max_reslut: 100

# OpenTelemetry 配置
telemetry:
  enabled: true
  service_name: secwalk
  service_version: v1.0.0
  environment: development
  otlp_endpoint: http://localhost:4318
  sample_rate: 1.0

# 热更新配置项
hot_swaps:
  - path: logger.level
    desc: 日志等级
    type: string
    validate: oneof=INFO|WARN|ERROR

  - path: feature.use_xguard
    desc: 是否开启风控检测
    type: bool
