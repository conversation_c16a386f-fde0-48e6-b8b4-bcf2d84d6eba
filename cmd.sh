#!/bin/bash

option=$1

if [ "$option" == "-h" ] || [ "$option" == "--help" ] ||[ "$option" == "" ]; then
    echo ""
    echo "[USAGE]: "
    echo "       sh cmd.sh [options]... <flags> [args...]"
    echo ""
    echo "[OPTIONS]:"
    echo "          install                         : 安装secwalk"
    echo "          up                              : 启动secwalk"
    echo "          down                            : 停止secwalk"
    exit 0
fi

if [ "$option" == "install" ]; then
    docker load < images/secwalk.tar.gz
fi

if [ "$option" == "up" ]; then
    mkdir -p $(pwd)/logs && chmod 0777 $(pwd)/logs && \
    docker run --name hn-secwalk \
        --restart always \
        --network hn-network \
        --log-opt max-file=7 \
        --log-opt max-size=20m \
        -v /etc/localtime:/etc/localtime \
        -v $(pwd)/logs/:/app/logs \
        -v $(pwd)/configs/:/app/configs \
        -e SECWALK_AGENT_KEYS="[{\"id\": 100,\"name\": \"AiLog-AIQL生成器\",\"aid\": \"f002f553-ac78-4d69-bae5-f7658b023c53\",\"key\": \"tcarZ6VUIl9BJZ8c\"},{\"id\": 101,\"name\": \"AiLog-日志解读\",\"aid\": \"0b41fea0-82fa-4af4-a52a-58a8702336cb\",\"key\": \"MHM54mEPPn5QeZkn\"},{\"id\": 102,\"name\": \"AiLog-日志上下文分析\",\"aid\": \"01d43778-7fc6-49d1-9f52-2d6f7312d9b2\",\"key\": \"qlvHNvrahpP1ZyWs\"},{\"id\":1,\"name\":\"告警研判智能体\",\"aid\":\"1da3469f-233c-486d-a6de-13b666050d46\",\"key\":\"c5c0eb8482881de2\"},{\"id\":2,\"name\":\"通用行业数据分类分级\",\"aid\":\"c0875de5-8b0b-4fe7-a084-24ee69447fb7\",\"key\":\"3a8f90f4beb75e03\"},{\"id\":3,\"name\":\"金融行业数据分类分级\",\"aid\":\"5f0852ae-86a3-4751-a026-fe20a1f2be1f\",\"key\":\"242985e519b3497e\"},{\"id\":4,\"name\":\"交通行业数据分类分级\",\"aid\":\"6274b030-941f-4a6c-81d5-039c49d017ff\",\"key\":\"b84c6c7b05b69583\"},{\"id\":5,\"name\":\"医疗行业数据分类分级\",\"aid\":\"8d4bca16-0cae-4e40-bb71-433f70725ee1\",\"key\":\"f409c48e8773e1ab\"},{\"id\":6,\"name\":\"工业行业数据分类分级\",\"aid\":\"12a3602a-bd5d-4b6b-a1e8-8a07abe4c0f9\",\"key\":\"c51227e6339d307c\"},{\"id\":7,\"name\":\"政府行业数据分类分级\",\"aid\":\"b8feccef-11f2-45d6-b083-647c2446114f\",\"key\":\"75d5679460ac9c86\"},{\"id\":8,\"name\":\"教育行业数据分类分级\",\"aid\":\"f14300d5-d424-4582-8629-169169504573\",\"key\":\"8d87fc12f45ecf75\"},{\"id\":9,\"name\":\"文档分类分级\",\"aid\":\"de2bed89-eac9-41fd-80c2-043529ba15a6\",\"key\":\"2a22f1bdde49cd29\"},{\"id\":10,\"name\":\"API接口风险研判\",\"aid\":\"ab4ba5f7-d0cf-41ae-92d0-07897680f299\",\"key\":\"d484dcf3a45acc90\"},{\"id\":11,\"name\":\"API接口打标\",\"aid\":\"f9cc3d84-a1f2-46fa-8f93-b64c9f51d2f0\",\"key\":\"4f316288d71183ef\"},{\"id\":12,\"name\":\"API接口聚合\",\"aid\":\"b78e7d49-3bc9-490a-b969-cdb3134796f8\",\"key\":\"5cc97e489d632e0a\"},{\"id\":13,\"name\":\"API接口识别\",\"aid\":\"5dafb0ae-6085-499b-a7d1-3f609f6cfca7\",\"key\":\"8702d2b6de5cb35a\"},{\"id\":14,\"name\":\"漏洞链解析智能体\",\"aid\":\"7a212a4a-9bb4-489b-9660-f2478caa4ef9\",\"key\":\"f2c76b6621dbaa7f\"},{\"id\":15,\"name\":\"ailpha-AIQL生成助手\",\"aid\":\"3abbb50d-1bdb-4397-8f17-0345f55175d9\",\"key\":\"f2c76b6621dbaa71\"},{\"id\":16,\"name\":\"SOAR_AIQL生成助手\",\"aid\":\"8560aabb-ac1a-4679-884d-fb517a2de050\",\"key\":\"AFinLNzw9gAT7P4M\"},{\"id\":17,\"name\":\"soar生成助手\",\"aid\":\"1916b5a2-a977-4827-8471-59df104d7587\",\"key\":\"JbdqkXMjj0sR9AVe\"},{\"id\":18,\"name\":\"报文研判智能体\",\"aid\":\"e323a9b3-b880-42dd-958c-68d95a484567\",\"key\":\"M1xYrhZ30GPLTC5X\"},{\"id\":19,\"name\":\"小恒智聊\",\"aid\":\"7144c59d-d44b-463d-9053-1a077c6d87b2\",\"key\":\"tqLwKw4Qa9BpIMbL\"},{\"id\":20,\"name\":\"漏洞查询智能体\",\"aid\":\"d64bb034-19bd-4acf-9b2a-64328d2b4553\",\"key\":\"eJfz3JWpvbi9MzjL\"},{\"id\":21,\"name\":\"恶意IoC情报查询智能体\",\"aid\":\"1b85cb6b-1f37-46c6-9519-dbfcf342e406\",\"key\":\"RTuoBjttlewH6x5F\"},{\"id\":22,\"name\":\"ailpha-产品售后助手\",\"aid\":\"7d97889d-ce6d-4882-a81b-846e3439deb2\",\"key\":\"RTuoIjttlewH6x5F\"},{\"id\":23,\"name\":\"ailpha-产品问答助手\",\"aid\":\"dffb1e7e-9b08-4e73-8fdf-35823bdeea25\",\"key\":\"M1xYrhZ50GPLTC5X\"},{\"id\":24,\"name\":\"ailpha-智能安全运营助手\",\"aid\":\"cde214a8-c2df-45a0-9ede-10123cc044d7\",\"key\":\"8d89fc12f45ecf75\"},{\"id\":25,\"name\":\"智能门户导航\",\"aid\":\"e315dad3-d853-4395-b021-8472e1082b00\",\"key\":\"tqLwKw9Qa9BpIMbL\"},{\"id\":26,\"name\":\"事件研判智能体\",\"aid\":\"294b531b-031a-49f5-93d0-b7e6dbf416c8\",\"key\":\"TqL9Kw9Qa9BpIMbL\"},{\"id\":27,\"name\":\"恶意样本分析智能体\",\"aid\":\"f2f41479-c95a-49c4-9904-16f83dfe8072\",\"key\":\"Taie7chs9Md5vSSO\"},{\"id\":28,\"name\":\"恶意IoC查询智能体\",\"aid\":\"c5844d4c-4913-4c70-b761-d290db930275\",\"key\":\"uvYyjmgmSuN39auZ\"},{\"id\":47,\"name\":\"IoC拓线分析智能体\",\"aid\":\"7107a7b4-964a-41b3-9886-af7d9302803d\",\"key\":\"F8aj8WdLAenqqtFZ\"},{\"id\":48,\"name\":\"告警数据质量分析智能体\",\"aid\":\"cc578ee9-0a13-41e4-a708-c4b64bef3531\",\"key\":\"BoMEpVHADaNpW8As\"},{\"id\":52,\"name\":\"API接口行为风险研判\",\"aid\":\"5f94cbb7-1bbf-4823-b9a1-521859799ae5\",\"key\":\"tZMsywl0bxNeIgM6\"},{\"id\":53,\"name\":\"API接口行为风险研判-单日超量获取数据\",\"aid\":\"5cf8705e-5e4b-4c36-9e8e-bd8da8f60683\",\"key\":\"w77ga9oKkwoRKZKu\"},{\"id\":54,\"name\":\"API接口行为风险研判-账号暴力破解\",\"aid\":\"b590e4f4-312a-4b1f-a68c-6481eb8879f7\",\"key\":\"4eV7NvlMs7JwLLmZ\"},{\"id\":55,\"name\":\"告警降噪智能体\",\"aid\":\"c039af05-3a4b-4791-8328-17f0b0112ae1\",\"key\":\"zUuAHTq7WhL6HsW9\"},{\"id\":56,\"name\":\"安全情报分析智能体\",\"aid\":\"585bf3d8-7d46-4ea3-81bb-0f2947defb6f\",\"key\":\"VnCZdDLnsMBH0Zx1\"},{\"id\":57,\"name\":\"文件流转日志分析\",\"aid\":\"956a38bc-161c-4816-b05a-d0cd1e9c39c8\",\"key\":\"lGVXZmSMXM3BK1pJ\"},{\"id\":58,\"name\":\"慢SQL优化\",\"aid\":\"9b9c3c63-dd98-4655-83a6-b96e11594a27\",\"key\":\"gpmwretx8pjDbZ3w\"},{\"id\":59,\"name\":\"数据库运维人员异常行为识别\",\"aid\":\"4d7ee3d5-c4d0-47fd-8eb3-f3e77598b0d0\",\"key\":\"WMFiaZmtNQu3fWHP\"},{\"id\":60,\"name\":\"应用SQL语句转自然语言\",\"aid\":\"8f353adb-5378-4ac5-b7fe-7d71633b5359\",\"key\":\"u9VjwhNKAkhbX9tq\"},{\"id\":61,\"name\":\"等保测评率统计智能体\",\"aid\":\"271c8d4a-9809-466c-9a47-1836f6099402\",\"key\":\"kPK8FToSGIppo8HZ\"},{\"id\":62,\"name\":\"编码识别及解码智能体-DeepSeek版\",\"aid\":\"091535f5-b6c6-49df-bf84-81e3a973f085\",\"key\":\"Oi1GZROiwqivRg8F\"},{\"id\":63,\"name\":\"代码编写执行智能体-DeepSeek版\",\"aid\":\"ae0b8c84-efc9-47f8-a004-0b40aec74625\",\"key\":\"r9YA4IV0ttsHDtLB\"},{\"id\":65,\"name\":\"网络安全事件报告-DeepSeek版\",\"aid\":\"913c4b62-4401-435d-bcf4-0711f6762ed0\",\"key\":\"KbAikhucZctwwJqu\"},{\"id\":66,\"name\":\"等保测评率智能体\",\"aid\":\"a5f6cf00-e992-43fb-9764-548f3fc00c5e\",\"key\":\"ZJSZEx5d1rvZRw8l\"},{\"id\":67,\"name\":\"安全规则编写智能体\",\"aid\":\"6643f29c-9c2a-447b-aa21-dcebfa973b34\",\"key\":\"nP9Xh07wUCVfLoDp\"},{\"id\":68,\"name\":\"渗透测试指导智能体\",\"aid\":\"e41768c7-2191-4110-a653-3b2bc8ec7721\",\"key\":\"lxHHrM0lDqqJPhaK\"},{\"id\":69,\"name\":\"钓鱼邮件编写智能体\",\"aid\":\"7db04318-ed99-46b4-8616-20e0f2463a00\",\"key\":\"oTw0FKDqsUY1kVYA\"},{\"id\":70,\"name\":\"钓鱼邮件识别智能体\",\"aid\":\"36a88d94-53e7-419b-a84d-73c23cb0a27c\",\"key\":\"FFdL8vASS0sqg0Gb\"},{\"id\":71,\"name\":\"恶意脚本分析智能体\",\"aid\":\"d2635351-7486-4e25-a524-cd0dad408978\",\"key\":\"G2m9ERTID8Xh6HOT\"},{\"id\":72,\"name\":\"API报文内容标签提取\",\"aid\":\"027a8213-9743-459f-8b8f-5da97da038b0\",\"key\":\"cCrWIuDva9xbA8LX\"},{\"id\":73,\"name\":\"代码风险审计智能体\",\"aid\":\"7b64df53-e9d3-43fa-a1de-b3819f62c2ab\",\"key\":\"lqEC2gj5tGKI79uv\"},{\"id\":74,\"name\":\"反诈报案协助智能体\",\"aid\":\"59334942-39ae-4579-8864-2ad31456bce2\",\"key\":\"TIfG11XxltLAqw96\"},{\"id\":75,\"name\":\"封禁任务构建智能体\",\"aid\":\"511ba1db-3f6e-447a-ad19-c642fda113f7\",\"key\":\"VBBuh48JOnezEmY4\"},{\"id\":76,\"name\":\"事件日志分析智能体\",\"aid\":\"6f38c8f2-fa38-43f2-a1d6-52fcbbb394e1\",\"key\":\"DtTrgfC2Ic2LO9Ao\"},{\"id\":77,\"name\":\"网络安全事件报告\",\"aid\":\"ab876b3f-0774-4ec4-a235-f8f5c29a0e4d\",\"key\":\"U9AAyTCoq8HGROeW\"},{\"id\":78,\"name\":\"中文乱码转换\",\"aid\":\"878f37b4-c866-41d3-bcac-89ef964b6339\",\"key\":\"AdU2eiqdfxitHnXC\"},{\"id\":79,\"name\":\"安全测试报告智能体\",\"aid\":\"3095dd28-7f1d-4a77-9b99-b49cb9d79b93\",\"key\":\"fhus2sONWxRDlw5t\"},{\"id\":80,\"name\":\"代码编写执行智能体\",\"aid\":\"29a13e2d-8541-4b2a-865c-03b2a636d900\",\"key\":\"GRVsrsaCkrRQiETp\"},{\"id\":81,\"name\":\"工作日报编写智能体\",\"aid\":\"061b8861-9d69-4615-8cf7-f4fa38e6f59c\",\"key\":\"EdBcLxvyhzkyWdSo\"},{\"id\":82,\"name\":\"工作月报编写智能体\",\"aid\":\"efc85d4b-083f-4b71-8e75-9f6fcb9187e5\",\"key\":\"jMJh1flgmgvNrj9M\"},{\"id\":83,\"name\":\"工作周报编写智能体\",\"aid\":\"9b72433a-86f1-43d0-9f2f-e2cabfbb1872\",\"key\":\"kyzMtO5d2zva8kLm\"},{\"id\":84,\"name\":\"信息安全解决方案专家\",\"aid\":\"b67af13b-8d36-4f25-8036-54441e2f4ef4\",\"key\":\"htQVU9QtCqwhYPeO\"},{\"id\":85,\"name\":\"通知公告编写智能体\",\"aid\":\"3f9628b8-1c91-493a-8498-5bdbe89ab521\",\"key\":\"uWDoRV8udHRNJsGM\"},{\"id\":86,\"name\":\"请示编写智能体\",\"aid\":\"422fc6ca-bffe-4e7a-be12-3479eb171f09\",\"key\":\"BM54SH0OGTvGnmOi\"},{\"id\":87,\"name\":\"会议纪要编写智能体\",\"aid\":\"5f1fc108-adb9-4bb1-90e9-2eb466194f74\",\"key\":\"cGZrqMFgVY5t7dUo\"},{\"id\":88,\"name\":\"发言稿编写智能体\",\"aid\":\"f5fc3c5d-8c8b-4377-8283-130e01003e80\",\"key\":\"w1fvvkWNnVInOAOs\"},{\"id\":89,\"name\":\"告警推理解析智能体\",\"aid\":\"09c689ea-9191-4529-bbd9-77f24c697e43\",\"key\":\"zWymm9oaTw9GRDkt\"},{\"id\":90,\"name\":\"渗透工具指导智能体\",\"aid\":\"73d311dc-cf62-4b48-a9c7-71abcab7a918\",\"key\":\"ZBElDet4M1sgsMeJ\"},{\"id\":91,\"name\":\"日志解读智能体\",\"aid\":\"fa2cf86b-e4ef-4ada-9fb5-37a9a2ff5ca1\",\"key\":\"6y4dKw3PuhQAj7YG\"},{\"id\":92,\"name\":\"内容改写智能体\",\"aid\":\"19033787-3fc1-493c-ad05-8b91fb09b849\",\"key\":\"jEVccUZE8HppA9aE\"},{\"id\":93,\"name\":\"混淆代码分析智能体\",\"aid\":\"6a870e02-3cfd-4736-992e-346e99875711\",\"key\":\"gqsAnLyAZO7aN0Gs\"},{\"id\":94,\"name\":\"备案和注册信息查询小助手\",\"aid\":\"3dc2a7bd-99d7-48a2-8c52-67ad41f7e40b\",\"key\":\"Ah02GurTpkc5AbaM\"},{\"id\":95,\"name\":\"编码识别及解码智能体\",\"aid\":\"331cbc29-bbc9-4b0e-97e9-204635bd4ed5\",\"key\":\"ObiRm4LuhKAtkuP1\"},{\"id\":96,\"name\":\"地方规范或标准编写\",\"aid\":\"45ea9737-4c63-42c3-b22a-72515b4e329a\",\"key\":\"d6m6nlNvszQaU71d\"},{\"id\":97,\"name\":\"方案报告编写智能体\",\"aid\":\"b0f64985-cee2-48b7-b775-e760e3c0f82b\",\"key\":\"PJZpY7zso053dRIp\"},{\"id\":98,\"name\":\"学习心得编写智能体\",\"aid\":\"5447ff40-d697-468a-96f2-e632e549e021\",\"key\":\"J2fvk6KkQC3LPZzE\"},{\"id\":99,\"name\":\"分类分级专家经验智能体\",\"aid\":\"601e0b4b-18af-4c59-a1dc-23ebd648c604\",\"key\":\"XW9A92fJkemdINKx\"}]" \
        -p 8998:8998 \
        -d docker.das-security.cn/secark/secwalk:latest \
        ./secwalk run \
        --config /secwalk/configs/prod.yaml \
        --consul hn-consul:8500 \
        --token d9295a36-9f55-4580-b22d-71cfa5837f76
fi

if [ "$option" == "down" ]; then
    docker stop hn-secwalk
    docker rm hn-secwalk
fi
