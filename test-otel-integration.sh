#!/bin/bash

# SecWalk OpenTelemetry 集成测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查编译
test_build() {
    log_info "测试编译..."
    
    if go build -o bin/secwalk cmd/main.go; then
        log_info "✓ 编译成功"
    else
        log_error "✗ 编译失败"
        exit 1
    fi
}

# 检查配置文件
test_config() {
    log_info "检查配置文件..."
    
    # 检查开发配置
    if [ -f "configs/dev.yaml" ]; then
        if grep -q "telemetry:" configs/dev.yaml; then
            log_info "✓ 开发配置包含 telemetry 配置"
        else
            log_warn "⚠ 开发配置缺少 telemetry 配置"
        fi
    else
        log_error "✗ 开发配置文件不存在"
    fi
    
    # 检查生产配置
    if [ -f "configs/pro.yaml" ]; then
        if grep -q "telemetry:" configs/pro.yaml; then
            log_info "✓ 生产配置包含 telemetry 配置"
        else
            log_warn "⚠ 生产配置缺少 telemetry 配置"
        fi
    else
        log_error "✗ 生产配置文件不存在"
    fi
}

# 检查 OTEL 配置文件
test_otel_config() {
    log_info "检查 OTEL Collector 配置..."
    
    if [ -f "otel-collector-config.yaml" ]; then
        log_info "✓ OTEL Collector 配置文件存在"
        
        # 检查关键配置项
        if grep -q "receivers:" otel-collector-config.yaml && \
           grep -q "processors:" otel-collector-config.yaml && \
           grep -q "exporters:" otel-collector-config.yaml; then
            log_info "✓ OTEL 配置文件结构正确"
        else
            log_warn "⚠ OTEL 配置文件可能不完整"
        fi
    else
        log_error "✗ OTEL Collector 配置文件不存在"
    fi
}

# 检查 Docker 配置
test_docker_config() {
    log_info "检查 Docker 配置..."
    
    if [ -f "docker-compose.otel.yaml" ]; then
        log_info "✓ Docker Compose 配置文件存在"
        
        # 检查服务定义
        if grep -q "aws-otel-collector:" docker-compose.otel.yaml && \
           grep -q "hn-secwalk:" docker-compose.otel.yaml; then
            log_info "✓ Docker Compose 包含必要的服务"
        else
            log_warn "⚠ Docker Compose 配置可能不完整"
        fi
    else
        log_error "✗ Docker Compose 配置文件不存在"
    fi
}

# 检查依赖
test_dependencies() {
    log_info "检查 Go 依赖..."
    
    if go mod verify; then
        log_info "✓ Go 模块验证通过"
    else
        log_error "✗ Go 模块验证失败"
        exit 1
    fi
    
    # 检查关键的 OpenTelemetry 依赖
    if go list -m go.opentelemetry.io/otel >/dev/null 2>&1; then
        log_info "✓ OpenTelemetry 核心库已安装"
    else
        log_error "✗ OpenTelemetry 核心库未安装"
    fi
    
    if go list -m go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin >/dev/null 2>&1; then
        log_info "✓ Gin OpenTelemetry 中间件已安装"
    else
        log_error "✗ Gin OpenTelemetry 中间件未安装"
    fi
}

# 测试服务启动（快速测试）
test_service_start() {
    log_info "测试服务启动..."
    
    # 创建临时配置文件，禁用外部依赖
    cat > test-config.yaml << EOF
server:
  name: secwalk
  address: :18998

logger:
  app: secwalk
  level: info
  format: false
  output: logs/secwalk.log
  max_size: 20
  max_age: 30
  max_backup: 10

telemetry:
  enabled: false  # 禁用以避免外部依赖
  service_name: secwalk
  service_version: v1.0.0
  environment: test
  otlp_endpoint: http://localhost:4318
  sample_rate: 1.0

feature:
  use_xguard: false
  max_reslut: 100
EOF
    
    # 尝试启动服务（后台运行）
    timeout 10s ./bin/secwalk run --config test-config.yaml &
    PID=$!
    
    # 等待一下
    sleep 3
    
    # 检查进程是否还在运行
    if kill -0 $PID 2>/dev/null; then
        log_info "✓ 服务启动成功"
        kill $PID 2>/dev/null || true
    else
        log_warn "⚠ 服务启动可能有问题（可能是因为缺少外部依赖）"
    fi
    
    # 清理
    rm -f test-config.yaml
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    echo ""
    echo "=========================================="
    echo "SecWalk OpenTelemetry 集成测试报告"
    echo "=========================================="
    echo ""
    echo "✓ 编译测试通过"
    echo "✓ 配置文件检查完成"
    echo "✓ OTEL 配置检查完成"
    echo "✓ Docker 配置检查完成"
    echo "✓ 依赖检查完成"
    echo "✓ 服务启动测试完成"
    echo ""
    echo "下一步操作："
    echo "1. 设置 AWS 环境变量："
    echo "   export AWS_REGION=your-region"
    echo "   export AWS_ACCESS_KEY_ID=your-access-key"
    echo "   export AWS_SECRET_ACCESS_KEY=your-secret-key"
    echo ""
    echo "2. 启动服务："
    echo "   ./deploy-with-otel.sh start"
    echo ""
    echo "3. 测试健康检查："
    echo "   curl http://localhost:8998/secwalk/v1/health"
    echo ""
    echo "4. 查看 OTEL Collector 指标："
    echo "   curl http://localhost:8888/metrics"
    echo ""
}

# 主函数
main() {
    log_info "开始 SecWalk OpenTelemetry 集成测试..."
    
    test_build
    test_config
    test_otel_config
    test_docker_config
    test_dependencies
    test_service_start
    generate_report
    
    log_info "所有测试完成！"
}

# 执行主函数
main "$@"
