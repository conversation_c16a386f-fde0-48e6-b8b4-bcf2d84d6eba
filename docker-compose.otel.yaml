version: '3.8'

services:
  # AWS OTEL Collector
  aws-otel-collector:
    image: public.ecr.aws/aws-observability/aws-otel-collector:latest
    container_name: aws-otel-collector
    command: ["--config=/etc/otel-agent-config.yaml"]
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-agent-config.yaml:ro
    ports:
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
      - "8888:8888"   # Prometheus metrics endpoint
      - "13133:13133" # Health check endpoint
    environment:
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}  # 如果使用临时凭证
    networks:
      - hn-network
    restart: unless-stopped
    depends_on:
      - secwalk
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:13133/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 您的 SecWalk 服务
  hn-secwalk:
    image: docker.das-security.cn/secark/secwalk:latest
    container_name: hn-secwalk
    command: ["./secwalk", "run", "--config", "/app/configs/pro.yaml"]
    ports:
      - "8998:8998"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./logs/:/app/logs
      - ./configs/:/app/configs:ro
    environment:
      # OpenTelemetry 环境变量 (通过配置文件控制，这里作为备用)
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://aws-otel-collector:4318
      - OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
      - OTEL_SERVICE_NAME=secwalk
      - OTEL_SERVICE_VERSION=v1.0.0
      - OTEL_RESOURCE_ATTRIBUTES=service.name=secwalk,service.version=v1.0.0,deployment.environment=production
    networks:
      - hn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8998/secwalk/v1/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  hn-network:
    external: true  # 使用您现有的网络

volumes:
  otel_data:
    driver: local
